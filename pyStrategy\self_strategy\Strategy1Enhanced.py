"""
Strategy1_Enhanced - 群论增强型模糊推理交易策略 (优化版本)
基于Strategy1_new.py的优化改进版本

主要优化内容:
1. 修复State类热更新问题 - 持仓后自适应止盈止损、风险水平、预测置信度实时更新
2. HULL和STC趋势平滑优化 - 减少频繁变动，增加趋势稳定性
3. 增加强趋势/普通趋势判断 - 提供更精细的趋势分析
4. 保持原有信号生成机制的良好效果

技术特点:
- 完整实现HULL和STC技术指标（带平滑优化）
- 群论数学工具内嵌于模糊推理决策
- 智能趋势强度分析
- 实时状态热更新机制
- 符合无限易Pro部署规范
"""

from typing import Literal, Dict, List, Optional, Tuple, Any
import numpy as np
import math

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    
    # 技术指标参数
    hull_period: int = Field(default=9, title="HULL周期")
    stc_fast: int = Field(default=23, title="STC快周期")
    stc_slow: int = Field(default=50, title="STC慢周期")
    stc_cycle: int = Field(default=10, title="STC循环周期")
    
    # 平滑优化参数
    hull_smooth_factor: float = Field(default=0.3, title="HULL平滑因子")
    stc_smooth_factor: float = Field(default=0.4, title="STC平滑因子")
    trend_confirmation_bars: int = Field(default=3, title="趋势确认K线数")
    
    # 趋势强度判断参数
    strong_trend_threshold: float = Field(default=0.7, title="强趋势阈值")
    trend_strength_period: int = Field(default=5, title="趋势强度计算周期")
    
    # 交易参数
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 自适应风险控制参数
    adaptive_risk_enabled: bool = Field(default=True, title="启用自适应风险控制")
    risk_sensitivity: float = Field(default=0.5, title="风险敏感度")
    risk_update_frequency: int = Field(default=1, title="风险更新频率(K线数)")
    
    # 模糊系统参数
    fuzzy_sensitivity: float = Field(default=0.5, title="模糊系统敏感度")
    
    # 信号阈值参数
    signal_threshold: float = Field(default=0.55, title="信号阈值")
    confidence_threshold: float = Field(default=0.6, title="置信度阈值")


class State(BaseState):
    """状态映射模型 - 增强版本（支持热更新）"""
    # HULL指标状态（平滑优化）
    hull_value: float = Field(default=0, title="HULL当前值")
    hull_prev: float = Field(default=0, title="HULL前值")
    hull_trend: str = Field(default="NEUTRAL", title="HULL趋势")
    hull_trend_strength: float = Field(default=0, title="HULL趋势强度")
    hull_trend_type: str = Field(default="NORMAL", title="HULL趋势类型")  # STRONG/NORMAL
    hull_smoothed_value: float = Field(default=0, title="HULL平滑值")
    
    # STC指标状态（平滑优化）
    stc_value: float = Field(default=0, title="STC当前值")
    stc_signal: float = Field(default=0, title="STC信号线")
    stc_trend: str = Field(default="NEUTRAL", title="STC趋势")
    stc_trend_strength: float = Field(default=0, title="STC趋势强度")
    stc_trend_type: str = Field(default="NORMAL", title="STC趋势类型")  # STRONG/NORMAL
    stc_smoothed_value: float = Field(default=0, title="STC平滑值")
    
    # 综合趋势分析
    combined_trend: str = Field(default="NEUTRAL", title="综合趋势")
    combined_trend_strength: float = Field(default=0, title="综合趋势强度")
    combined_trend_type: str = Field(default="NORMAL", title="综合趋势类型")
    
    # 模糊系统状态
    fuzzy_signal: float = Field(default=0, title="模糊信号值")
    fuzzy_confidence: float = Field(default=0, title="模糊置信度")
    fuzzy_action: str = Field(default="HOLD", title="模糊行动建议")
    
    # 实时预测状态（热更新支持）
    prediction_active: bool = Field(default=False, title="预测功能激活状态")
    predicted_price: float = Field(default=0, title="预测价格")
    prediction_confidence: float = Field(default=0, title="预测置信度")
    prediction_horizon: int = Field(default=5, title="预测时间窗口")
    prediction_last_update: int = Field(default=0, title="预测最后更新时间")
    
    # 自适应风险控制状态（热更新支持）
    adaptive_stop_loss: float = Field(default=0, title="自适应止损价格")
    adaptive_take_profit: float = Field(default=0, title="自适应止盈价格")
    risk_level: float = Field(default=0, title="当前风险水平")
    risk_last_update: int = Field(default=0, title="风险最后更新时间")
    
    # 综合决策状态
    final_signal: float = Field(default=0, title="最终信号强度")
    signal_confidence: float = Field(default=0, title="信号置信度")
    trading_action: str = Field(default="HOLD", title="交易行动")
    
    # 状态更新计数器
    kline_count: int = Field(default=0, title="K线计数器")


class Strategy1Enhanced(BaseStrategy):
    """群论增强型模糊推理交易策略 - 优化版本"""
    
    def __init__(self):
        super().__init__()
        
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.short_signal: bool = False
        self.cover_signal: bool = False
        
        # 价格数据
        self.tick: TickData = None
        
        # 订单管理
        self.order_id = None
        self.signal_price = 0
        self.long_price = None
        self.short_price = None
        
        # HULL指标数据存储（增强版）
        self.hull_prices = []
        self.hull_wma_half = []
        self.hull_wma_full = []
        self.hull_values = []
        self.hull_trend_history = []  # 趋势历史记录
        
        # STC指标数据存储（增强版）
        self.stc_prices = []
        self.stc_macd_values = []
        self.stc_stoch1_values = []
        self.stc_stoch2_values = []
        self.stc_signal_line = 0.0
        self.stc_trend_history = []  # 趋势历史记录
        
        # 平滑处理缓存
        self.hull_smooth_cache = []
        self.stc_smooth_cache = []
        
        # 趋势强度分析缓存
        self.trend_strength_cache = []
        
        # 实时预测系统（热更新支持）
        self.prediction_system = None
        self.prediction_history = []
        
        # 自适应风险控制系统（热更新支持）
        self.adaptive_risk_controller = None
        
        # 群论运算缓存
        self.group_cache = {}
        
        # 模糊推理规则
        self.fuzzy_rules = self._initialize_enhanced_fuzzy_rules()
        
        # 初始化智能化组件
        self._initialize_intelligent_components()
    
    def _initialize_enhanced_fuzzy_rules(self) -> List[Dict]:
        """初始化增强的模糊推理规则（支持趋势强度）"""
        return [
            # 强趋势规则 - 更高权重
            {"hull": "bullish", "stc": "bullish", "hull_type": "strong", "stc_type": "strong", 
             "output": "strong_buy", "weight": 1.2, "group_factor": 1.3},
            {"hull": "bearish", "stc": "bearish", "hull_type": "strong", "stc_type": "strong", 
             "output": "strong_sell", "weight": 1.2, "group_factor": 1.3},
            
            # 强趋势与普通趋势组合
            {"hull": "bullish", "stc": "bullish", "hull_type": "strong", "stc_type": "normal", 
             "output": "buy", "weight": 1.0, "group_factor": 1.2},
            {"hull": "bearish", "stc": "bearish", "hull_type": "strong", "stc_type": "normal", 
             "output": "sell", "weight": 1.0, "group_factor": 1.2},
            
            # 普通趋势规则
            {"hull": "bullish", "stc": "bullish", "hull_type": "normal", "stc_type": "normal", 
             "output": "buy", "weight": 0.8, "group_factor": 1.1},
            {"hull": "bearish", "stc": "bearish", "hull_type": "normal", "stc_type": "normal", 
             "output": "sell", "weight": 0.8, "group_factor": 1.1},
            
            # 趋势不一致但有强趋势支持
            {"hull": "bullish", "stc": "neutral", "hull_type": "strong", "stc_type": "any", 
             "output": "buy", "weight": 0.7, "group_factor": 1.0},
            {"hull": "bearish", "stc": "neutral", "hull_type": "strong", "stc_type": "any", 
             "output": "sell", "weight": 0.7, "group_factor": 1.0},
            
            # 中性规则
            {"hull": "neutral", "stc": "neutral", "hull_type": "any", "stc_type": "any", 
             "output": "hold", "weight": 1.0, "group_factor": 1.0},
        ]
    
    def _initialize_intelligent_components(self):
        """初始化智能化组件（热更新支持）"""
        # 实时预测系统（支持热更新）
        self.prediction_system = self.EnhancedGroupTheoryPredictionSystem()
        
        # 自适应风险控制系统（支持热更新）
        self.adaptive_risk_controller = self.EnhancedAdaptiveRiskController()
    
    class EnhancedGroupTheoryPredictionSystem:
        """增强的群论预测系统（支持热更新）"""
        
        def __init__(self):
            self.is_active = False
            self.prediction_window = 5
            self.price_memory = []
            self.group_features_memory = []
            self.prediction_accuracy_history = []
            self.last_update_time = 0
            self.update_interval = 1  # 每K线更新一次
            
        def activate(self, current_position):
            """激活预测系统（仅在持仓时）"""
            self.is_active = current_position != 0
            if self.is_active:
                self.price_memory = []
                self.group_features_memory = []
                self.last_update_time = 0
        
        def deactivate(self):
            """停用预测系统"""
            self.is_active = False
            self.price_memory = []
            self.group_features_memory = []
            self.last_update_time = 0
        
        def should_update(self, current_time):
            """判断是否需要更新预测（热更新控制）"""
            if not self.is_active:
                return False
            return current_time - self.last_update_time >= self.update_interval
        
        def predict_next_prices(self, current_price, hull_prices, group_features, current_time):
            """基于群论特征预测未来价格（支持热更新）"""
            if not self.is_active:
                return 0.0, 0.0
            
            # 热更新控制
            if not self.should_update(current_time):
                # 返回缓存的预测结果
                if hasattr(self, '_cached_prediction'):
                    return self._cached_prediction
                return current_price, 0.0
            
            # 更新记忆
            self.price_memory.append(current_price)
            self.group_features_memory.append(group_features)
            
            # 限制记忆长度
            if len(self.price_memory) > 20:
                self.price_memory = self.price_memory[-20:]
                self.group_features_memory = self.group_features_memory[-20:]
            
            if len(self.price_memory) < 5:
                self._cached_prediction = (current_price, 0.0)
                self.last_update_time = current_time
                return current_price, 0.0
            
            # 群论增强的价格预测
            predicted_price = self._group_theory_price_prediction(
                self.price_memory, self.group_features_memory
            )
            
            # 计算预测置信度
            confidence = self._calculate_prediction_confidence(
                self.price_memory, predicted_price
            )
            
            # 缓存预测结果
            self._cached_prediction = (predicted_price, confidence)
            self.last_update_time = current_time
            
            return predicted_price, confidence
        
        def _group_theory_price_prediction(self, prices, group_features):
            """基于群论特征的价格预测（优化版本）"""
            if len(prices) < 3:
                return prices[-1]
            
            # 提取最近的群论特征
            recent_features = group_features[-3:]
            recent_prices = prices[-3:]
            
            # 群论对称性预测（增强版）
            symmetry_prediction = self._enhanced_symmetry_prediction(recent_prices)
            
            # 群不变量预测（增强版）
            invariant_prediction = self._enhanced_invariant_prediction(recent_prices, recent_features)
            
            # 李群变换预测（增强版）
            lie_group_prediction = self._enhanced_lie_group_prediction(recent_prices)
            
            # 置换群模式预测（增强版）
            permutation_prediction = self._enhanced_permutation_prediction(recent_prices)
            
            # 动态权重融合（基于预测准确性历史）
            weights = self._calculate_dynamic_weights()
            predictions = [
                symmetry_prediction,
                invariant_prediction,
                lie_group_prediction,
                permutation_prediction
            ]
            
            # 计算加权平均预测
            weighted_prediction = sum(w * p for w, p in zip(weights, predictions))
            
            return weighted_prediction
        
        def _calculate_dynamic_weights(self):
            """计算动态权重（基于历史准确性）"""
            if len(self.prediction_accuracy_history) < 4:
                return [0.3, 0.3, 0.2, 0.2]  # 默认权重
            
            # 基于最近的预测准确性调整权重
            recent_accuracy = self.prediction_accuracy_history[-4:]
            total_accuracy = sum(recent_accuracy)
            
            if total_accuracy > 0:
                normalized_weights = [acc / total_accuracy for acc in recent_accuracy]
                return normalized_weights
            else:
                return [0.25, 0.25, 0.25, 0.25]  # 均等权重
        
        def _enhanced_symmetry_prediction(self, prices):
            """增强的对称性预测"""
            if len(prices) < 3:
                return prices[-1]
            
            # 多尺度对称性分析
            short_term_symmetry = self._calculate_symmetry(prices[-3:])
            medium_term_symmetry = self._calculate_symmetry(prices[-5:] if len(prices) >= 5 else prices)
            
            # 综合对称性因子
            combined_symmetry = 0.6 * short_term_symmetry + 0.4 * medium_term_symmetry
            
            # 基于对称性预测价格变化
            last_change = prices[-1] - prices[-2]
            predicted_change = last_change * (1 + combined_symmetry * 0.2)
            
            return prices[-1] + predicted_change
        
        def _calculate_symmetry(self, price_sequence):
            """计算价格序列的对称性"""
            if len(price_sequence) < 3:
                return 0.0
            
            center = len(price_sequence) // 2
            left_part = price_sequence[:center]
            right_part = price_sequence[center:][::-1]
            
            if len(left_part) == len(right_part) and len(left_part) > 1:
                correlation = np.corrcoef(left_part, right_part)[0, 1]
                return correlation if not np.isnan(correlation) else 0.0
            
            return 0.0
        
        def _enhanced_invariant_prediction(self, prices, features):
            """增强的不变量预测"""
            if len(prices) < 3 or len(features) < 3:
                return prices[-1]
            
            # 多层次不变量分析
            invariant_values = [f.get('invariant_factor', 0) for f in features]
            
            if len(invariant_values) >= 3:
                # 不变量趋势分析
                recent_trend = invariant_values[-1] - invariant_values[-2]
                medium_trend = np.mean(np.diff(invariant_values[-3:]))
                
                # 综合趋势
                combined_trend = 0.7 * recent_trend + 0.3 * medium_trend
                
                # 价格敏感度（自适应）
                price_volatility = np.std(prices[-5:]) if len(prices) >= 5 else np.std(prices)
                price_sensitivity = 0.01 * (1 + price_volatility / np.mean(prices))
                
                predicted_price = prices[-1] + combined_trend * price_sensitivity * prices[-1]
                return predicted_price
            
            return prices[-1]
        
        def _enhanced_lie_group_prediction(self, prices):
            """增强的李群变换预测"""
            if len(prices) < 4:
                return prices[-1]
            
            # 多阶李群变换
            log_returns = []
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    log_return = math.log(prices[i] / prices[i-1])
                    log_returns.append(log_return)
            
            if len(log_returns) >= 3:
                # 李群指数映射预测（多阶）
                first_order = log_returns[-1]
                second_order = log_returns[-1] - log_returns[-2] if len(log_returns) >= 2 else 0
                third_order = (log_returns[-1] - 2*log_returns[-2] + log_returns[-3]) if len(log_returns) >= 3 else 0
                
                # 综合李群预测
                predicted_log_return = first_order + 0.5 * second_order + 0.2 * third_order
                
                # 转换回价格
                predicted_price = prices[-1] * math.exp(predicted_log_return)
                return predicted_price
            
            return prices[-1]
        
        def _enhanced_permutation_prediction(self, prices):
            """增强的置换群模式预测"""
            if len(prices) < 5:
                return prices[-1]
            
            # 多尺度置换模式分析
            short_pattern = self._analyze_permutation_pattern(prices[-3:])
            medium_pattern = self._analyze_permutation_pattern(prices[-5:])
            
            # 模式强度加权
            if short_pattern['strength'] > medium_pattern['strength']:
                dominant_pattern = short_pattern
                weight = 0.7
            else:
                dominant_pattern = medium_pattern
                weight = 0.3
            
            # 基于主导模式预测
            if dominant_pattern['strength'] > 0.3:
                pattern_change = dominant_pattern['predicted_change']
                predicted_price = prices[-1] + pattern_change * weight
                return predicted_price
            
            return prices[-1]
        
        def _analyze_permutation_pattern(self, price_sequence):
            """分析置换模式"""
            if len(price_sequence) < 3:
                return {'strength': 0.0, 'predicted_change': 0.0}
            
            # 计算排名序列
            sorted_indices = np.argsort(price_sequence)
            ranks = np.empty_like(sorted_indices)
            ranks[sorted_indices] = np.arange(len(price_sequence))
            
            # 分析排名变化模式
            rank_changes = np.diff(ranks)
            
            if len(rank_changes) > 0:
                # 模式强度
                pattern_strength = 1.0 - (np.std(rank_changes) / (len(price_sequence) + 1))
                
                # 预测变化
                avg_rank_change = np.mean(rank_changes)
                price_changes = np.diff(price_sequence)
                avg_price_change = np.mean(price_changes) if len(price_changes) > 0 else 0
                
                predicted_change = avg_price_change * (1 + avg_rank_change * 0.1)
                
                return {
                    'strength': max(0.0, pattern_strength),
                    'predicted_change': predicted_change
                }
            
            return {'strength': 0.0, 'predicted_change': 0.0}
        
        def _calculate_prediction_confidence(self, prices, predicted_price):
            """计算预测置信度（增强版本）"""
            if len(prices) < 3:
                return 0.0
            
            # 多因子置信度计算
            
            # 1. 价格稳定性因子
            recent_prices = prices[-5:] if len(prices) >= 5 else prices
            price_volatility = np.std(recent_prices) / np.mean(recent_prices)
            stability_factor = 1.0 / (1.0 + price_volatility * 10)
            
            # 2. 预测偏差因子
            prediction_deviation = abs(predicted_price - prices[-1]) / prices[-1]
            deviation_factor = 1.0 / (1.0 + prediction_deviation * 20)
            
            # 3. 历史准确性因子
            accuracy_factor = 1.0
            if len(self.prediction_accuracy_history) > 0:
                recent_accuracy = np.mean(self.prediction_accuracy_history[-5:])
                accuracy_factor = 0.5 + recent_accuracy * 0.5
            
            # 4. 趋势一致性因子
            if len(prices) >= 3:
                recent_trend = prices[-1] - prices[-2]
                predicted_trend = predicted_price - prices[-1]
                
                if recent_trend * predicted_trend > 0:  # 同向
                    consistency_factor = 1.2
                else:  # 反向
                    consistency_factor = 0.8
            else:
                consistency_factor = 1.0
            
            # 综合置信度
            confidence = stability_factor * deviation_factor * accuracy_factor * consistency_factor
            
            return min(1.0, max(0.0, confidence))
    
    class EnhancedAdaptiveRiskController:
        """增强的自适应风险控制系统（支持热更新）"""
        
        def __init__(self):
            self.volatility_window = 20
            self.risk_memory = []
            self.performance_memory = []
            self.last_update_time = 0
            self.update_interval = 1  # 每K线更新一次
            self._cached_risk_levels = None
            
        def should_update(self, current_time, update_frequency):
            """判断是否需要更新风险水平（热更新控制）"""
            return current_time - self.last_update_time >= update_frequency
        
        def calculate_adaptive_levels(self, current_price, entry_price, position_direction, 
                                    market_volatility, prediction_confidence, hull_trend, stc_trend,
                                    hull_trend_strength, stc_trend_strength, current_time, update_frequency):
            """计算自适应止盈止损水平（支持热更新）"""
            
            # 热更新控制
            if not self.should_update(current_time, update_frequency) and self._cached_risk_levels:
                return self._cached_risk_levels
            
            # 基础风险参数
            base_risk_ratio = 0.02  # 基础风险比例
            base_profit_ratio = 0.04  # 基础盈利比例
            
            # 市场波动率调整（增强版）
            volatility_adjustment = min(2.5, max(0.4, market_volatility * 60))
            
            # 预测置信度调整（增强版）
            confidence_adjustment = 0.4 + prediction_confidence * 0.6
            
            # 趋势一致性和强度调整（新增）
            trend_consistency = self._calculate_enhanced_trend_consistency(
                hull_trend, stc_trend, hull_trend_strength, stc_trend_strength
            )
            trend_adjustment = 0.6 + trend_consistency * 0.8
            
            # 计算自适应止损水平
            adaptive_stop_ratio = base_risk_ratio * volatility_adjustment / confidence_adjustment
            adaptive_stop_ratio = min(0.06, max(0.008, adaptive_stop_ratio))  # 限制在0.8%-6%
            
            # 计算自适应止盈水平
            adaptive_profit_ratio = base_profit_ratio * trend_adjustment * confidence_adjustment
            adaptive_profit_ratio = min(0.12, max(0.015, adaptive_profit_ratio))  # 限制在1.5%-12%
            
            # 根据持仓方向计算具体价格
            if position_direction > 0:  # 多头
                stop_loss_price = entry_price * (1 - adaptive_stop_ratio)
                take_profit_price = entry_price * (1 + adaptive_profit_ratio)
            else:  # 空头
                stop_loss_price = entry_price * (1 + adaptive_stop_ratio)
                take_profit_price = entry_price * (1 - adaptive_profit_ratio)
            
            # 计算当前风险水平
            current_risk = abs(current_price - entry_price) / entry_price
            risk_level = current_risk / adaptive_stop_ratio
            
            # 缓存结果
            self._cached_risk_levels = {
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'risk_level': risk_level,
                'adaptive_stop_ratio': adaptive_stop_ratio,
                'adaptive_profit_ratio': adaptive_profit_ratio
            }
            
            self.last_update_time = current_time
            
            return self._cached_risk_levels
        
        def _calculate_enhanced_trend_consistency(self, hull_trend, stc_trend, hull_strength, stc_strength):
            """计算增强的趋势一致性（考虑趋势强度）"""
            # 基础一致性
            if hull_trend == stc_trend:
                if hull_trend in ["BULLISH", "BEARISH"]:
                    base_consistency = 1.0  # 强趋势一致
                else:
                    base_consistency = 0.5  # 中性一致
            else:
                base_consistency = 0.0  # 趋势不一致
            
            # 趋势强度加权
            avg_strength = (hull_strength + stc_strength) / 2
            strength_factor = 0.5 + avg_strength * 0.5
            
            # 综合一致性
            enhanced_consistency = base_consistency * strength_factor
            
            return enhanced_consistency
        
        def should_adjust_levels(self, current_risk_level, market_change_rate, trend_change):
            """判断是否需要调整止盈止损水平（增强版）"""
            # 风险水平过高时调整
            if current_risk_level > 0.75:
                return True
            
            # 市场快速变化时调整
            if abs(market_change_rate) > 0.025:
                return True
            
            # 趋势发生变化时调整
            if trend_change:
                return True
            
            return False

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            "HULL": self.state_map.hull_value,
            "HULL_SMOOTH": self.state_map.hull_smoothed_value,
            "STC": self.state_map.stc_value,
            "STC_SMOOTH": self.state_map.stc_smoothed_value,
        }
    
    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标（增强版）"""
        return {
            "STC_VALUE": self.state_map.stc_value,
            "STC_SIGNAL": self.state_map.stc_signal,
            "FUZZY_SIGNAL": self.state_map.fuzzy_signal,
            "PREDICTION_CONFIDENCE": self.state_map.prediction_confidence,
            "RISK_LEVEL": self.state_map.risk_level,
            "ADAPTIVE_STOP": self.state_map.adaptive_stop_loss,
            "ADAPTIVE_PROFIT": self.state_map.adaptive_take_profit,
            "HULL_TREND_STRENGTH": self.state_map.hull_trend_strength,
            "STC_TREND_STRENGTH": self.state_map.stc_trend_strength,
            "COMBINED_TREND_STRENGTH": self.state_map.combined_trend_strength,
        } 
   
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)
    
    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None
    
    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交回调"""
        super().on_trade(trade, log)
        self.order_id = None
    
    def on_start(self):
        """策略启动"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        
        super().on_start()
        
        # 初始化信号
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        
        # 清空数据存储
        self.hull_prices = []
        self.hull_wma_half = []
        self.hull_wma_full = []
        self.hull_values = []
        self.hull_trend_history = []
        self.stc_prices = []
        self.stc_macd_values = []
        self.stc_stoch1_values = []
        self.stc_stoch2_values = []
        self.stc_trend_history = []
        self.group_cache = {}
        
        # 清空平滑缓存
        self.hull_smooth_cache = []
        self.stc_smooth_cache = []
        self.trend_strength_cache = []
        
        # 重置智能化系统
        self.prediction_system.deactivate()
        self.prediction_history = []
        
        # 重置状态计数器
        self.state_map.kline_count = 0
        
        self.update_status_bar()
    
    def on_stop(self):
        """策略停止"""
        super().on_stop()
    
    def callback(self, kline: KLineData) -> None:
        """K线回调"""
        # 更新K线计数器
        self.state_map.kline_count += 1
        
        # 计算指标
        self.calc_indicator(kline)
        
        # 计算信号
        self.calc_signal(kline)
        
        # 信号执行
        self.exec_signal()
        
        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        if self.trading:
            self.update_status_bar()
    
    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        self.calc_indicator(kline)
        
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        self.update_status_bar()
    
    def calc_indicator(self, kline: KLineData) -> None:
        """计算技术指标（增强版本）"""
        # 更新价格数据
        self.hull_prices.append(kline.close)
        self.stc_prices.append(kline.close)
        
        # 限制历史数据长度
        max_length = max(self.params_map.hull_period, self.params_map.stc_slow) + 50
        if len(self.hull_prices) > max_length:
            self.hull_prices = self.hull_prices[-max_length:]
        if len(self.stc_prices) > max_length:
            self.stc_prices = self.stc_prices[-max_length:]
        
        # 计算HULL移动平均线（平滑优化版）
        self._calc_enhanced_hull_ma()
        
        # 计算STC指标（平滑优化版）
        self._calc_enhanced_stc()
        
        # 计算趋势强度分析
        self._calc_trend_strength_analysis()
        
        # 智能化预测和风险控制（热更新支持）
        self._update_intelligent_systems_with_hot_update(kline)
    
    def _calc_enhanced_hull_ma(self) -> None:
        """计算Hull移动平均线 - 平滑优化版本"""
        if len(self.hull_prices) < self.params_map.hull_period:
            return
        
        period = self.params_map.hull_period
        half_period = max(1, period // 2)
        sqrt_period = max(1, int(math.sqrt(period)))
        
        # 计算WMA(price, period/2)
        wma_half = self._weighted_moving_average(self.hull_prices, half_period)
        self.hull_wma_half.append(wma_half)
        
        # 计算WMA(price, period)
        wma_full = self._weighted_moving_average(self.hull_prices, period)
        self.hull_wma_full.append(wma_full)
        
        # 计算Hull MA: WMA(2*WMA(n/2) - WMA(n), sqrt(n))
        hull_raw = 2 * wma_half - wma_full
        self.hull_values.append(hull_raw)
        
        # 限制数组长度
        if len(self.hull_values) > sqrt_period + 10:
            self.hull_values = self.hull_values[-(sqrt_period + 10):]
        
        if len(self.hull_values) >= sqrt_period:
            # 计算最终Hull MA值
            self.state_map.hull_prev = self.state_map.hull_value
            raw_hull_value = self._weighted_moving_average(self.hull_values, sqrt_period)
            
            # 应用平滑处理
            self.state_map.hull_value = raw_hull_value
            self.state_map.hull_smoothed_value = self._apply_smoothing(
                raw_hull_value, self.hull_smooth_cache, self.params_map.hull_smooth_factor
            )
            
            # 更新趋势（基于平滑值和确认机制）
            self._update_hull_trend_with_confirmation()
    
    def _calc_enhanced_stc(self) -> None:
        """计算Schaff趋势周期指标 - 平滑优化版本"""
        if len(self.stc_prices) < self.params_map.stc_slow:
            return
        
        # 计算MACD
        fast_ema = self._exponential_moving_average(self.stc_prices, self.params_map.stc_fast)
        slow_ema = self._exponential_moving_average(self.stc_prices, self.params_map.stc_slow)
        macd = fast_ema - slow_ema
        self.stc_macd_values.append(macd)
        
        # 限制数组长度
        if len(self.stc_macd_values) > self.params_map.stc_cycle + 10:
            self.stc_macd_values = self.stc_macd_values[-(self.params_map.stc_cycle + 10):]
        
        if len(self.stc_macd_values) < self.params_map.stc_cycle:
            return
        
        # 第一次随机化
        stoch1 = self._stochastic_oscillator(self.stc_macd_values, self.params_map.stc_cycle)
        self.stc_stoch1_values.append(stoch1)
        
        # 限制数组长度
        if len(self.stc_stoch1_values) > self.params_map.stc_cycle + 10:
            self.stc_stoch1_values = self.stc_stoch1_values[-(self.params_map.stc_cycle + 10):]
        
        if len(self.stc_stoch1_values) < self.params_map.stc_cycle:
            return
        
        # 第二次随机化
        stoch2 = self._stochastic_oscillator(self.stc_stoch1_values, self.params_map.stc_cycle)
        
        # 应用平滑处理
        raw_stc_value = stoch2
        self.state_map.stc_value = raw_stc_value
        self.state_map.stc_smoothed_value = self._apply_smoothing(
            raw_stc_value, self.stc_smooth_cache, self.params_map.stc_smooth_factor
        )
        
        # 计算信号线（EMA平滑）
        alpha = 2.0 / (self.params_map.stc_cycle + 1)
        self.stc_signal_line = alpha * self.state_map.stc_smoothed_value + (1 - alpha) * self.stc_signal_line
        self.state_map.stc_signal = self.stc_signal_line
        
        # 更新趋势（基于平滑值和确认机制）
        self._update_stc_trend_with_confirmation()
    
    def _apply_smoothing(self, raw_value: float, cache: List[float], smooth_factor: float) -> float:
        """应用平滑处理"""
        cache.append(raw_value)
        
        # 限制缓存长度
        if len(cache) > 10:
            cache = cache[-10:]
        
        if len(cache) == 1:
            return raw_value
        
        # 指数平滑
        smoothed_value = smooth_factor * raw_value + (1 - smooth_factor) * cache[-2]
        
        return smoothed_value
    
    def _update_hull_trend_with_confirmation(self) -> None:
        """更新HULL趋势（带确认机制）"""
        if self.state_map.hull_smoothed_value > self.state_map.hull_prev:
            new_trend = "BULLISH"
        elif self.state_map.hull_smoothed_value < self.state_map.hull_prev:
            new_trend = "BEARISH"
        else:
            new_trend = "NEUTRAL"
        
        # 添加到趋势历史
        self.hull_trend_history.append(new_trend)
        
        # 限制历史长度
        if len(self.hull_trend_history) > self.params_map.trend_confirmation_bars + 5:
            self.hull_trend_history = self.hull_trend_history[-(self.params_map.trend_confirmation_bars + 5):]
        
        # 趋势确认机制
        if len(self.hull_trend_history) >= self.params_map.trend_confirmation_bars:
            recent_trends = self.hull_trend_history[-self.params_map.trend_confirmation_bars:]
            
            # 计算趋势一致性
            bullish_count = recent_trends.count("BULLISH")
            bearish_count = recent_trends.count("BEARISH")
            
            # 确认趋势
            if bullish_count >= self.params_map.trend_confirmation_bars * 0.7:
                confirmed_trend = "BULLISH"
            elif bearish_count >= self.params_map.trend_confirmation_bars * 0.7:
                confirmed_trend = "BEARISH"
            else:
                confirmed_trend = "NEUTRAL"
            
            # 只有在趋势确认后才更新
            if confirmed_trend != self.state_map.hull_trend:
                self.state_map.hull_trend = confirmed_trend
    
    def _update_stc_trend_with_confirmation(self) -> None:
        """更新STC趋势（带确认机制）"""
        if self.state_map.stc_smoothed_value > self.state_map.stc_signal:
            new_trend = "BULLISH"
        elif self.state_map.stc_smoothed_value < self.state_map.stc_signal:
            new_trend = "BEARISH"
        else:
            new_trend = "NEUTRAL"
        
        # 添加到趋势历史
        self.stc_trend_history.append(new_trend)
        
        # 限制历史长度
        if len(self.stc_trend_history) > self.params_map.trend_confirmation_bars + 5:
            self.stc_trend_history = self.stc_trend_history[-(self.params_map.trend_confirmation_bars + 5):]
        
        # 趋势确认机制
        if len(self.stc_trend_history) >= self.params_map.trend_confirmation_bars:
            recent_trends = self.stc_trend_history[-self.params_map.trend_confirmation_bars:]
            
            # 计算趋势一致性
            bullish_count = recent_trends.count("BULLISH")
            bearish_count = recent_trends.count("BEARISH")
            
            # 确认趋势
            if bullish_count >= self.params_map.trend_confirmation_bars * 0.7:
                confirmed_trend = "BULLISH"
            elif bearish_count >= self.params_map.trend_confirmation_bars * 0.7:
                confirmed_trend = "BEARISH"
            else:
                confirmed_trend = "NEUTRAL"
            
            # 只有在趋势确认后才更新
            if confirmed_trend != self.state_map.stc_trend:
                self.state_map.stc_trend = confirmed_trend
    
    def _calc_trend_strength_analysis(self) -> None:
        """计算趋势强度分析"""
        if len(self.hull_prices) < self.params_map.trend_strength_period:
            return
        
        # 计算HULL趋势强度
        self.state_map.hull_trend_strength = self._calculate_trend_strength(
            self.hull_prices, self.params_map.trend_strength_period, "hull"
        )
        
        # 计算STC趋势强度
        if len(self.stc_stoch2_values) >= self.params_map.trend_strength_period:
            stc_values = [self.state_map.stc_value] * self.params_map.trend_strength_period
            self.state_map.stc_trend_strength = self._calculate_trend_strength(
                stc_values, self.params_map.trend_strength_period, "stc"
            )
        
        # 判断趋势类型
        self.state_map.hull_trend_type = "STRONG" if self.state_map.hull_trend_strength > self.params_map.strong_trend_threshold else "NORMAL"
        self.state_map.stc_trend_type = "STRONG" if self.state_map.stc_trend_strength > self.params_map.strong_trend_threshold else "NORMAL"
        
        # 计算综合趋势
        self._calculate_combined_trend()
    
    def _calculate_trend_strength(self, values: List[float], period: int, indicator_type: str) -> float:
        """计算趋势强度"""
        if len(values) < period:
            return 0.0
        
        recent_values = values[-period:]
        
        # 计算趋势方向一致性
        changes = [recent_values[i+1] - recent_values[i] for i in range(len(recent_values)-1)]
        
        if not changes:
            return 0.0
        
        # 计算方向一致性
        positive_changes = sum(1 for change in changes if change > 0)
        negative_changes = sum(1 for change in changes if change < 0)
        total_changes = len(changes)
        
        direction_consistency = max(positive_changes, negative_changes) / total_changes
        
        # 计算变化幅度
        avg_change = np.mean([abs(change) for change in changes])
        avg_value = np.mean(recent_values)
        
        if avg_value > 0:
            magnitude_factor = avg_change / avg_value
        else:
            magnitude_factor = 0.0
        
        # 综合趋势强度
        trend_strength = direction_consistency * (1 + magnitude_factor * 10)
        
        return min(1.0, trend_strength)
    
    def _calculate_combined_trend(self) -> None:
        """计算综合趋势"""
        # 综合趋势方向
        if self.state_map.hull_trend == self.state_map.stc_trend:
            self.state_map.combined_trend = self.state_map.hull_trend
        else:
            # 以强度更高的趋势为准
            if self.state_map.hull_trend_strength > self.state_map.stc_trend_strength:
                self.state_map.combined_trend = self.state_map.hull_trend
            elif self.state_map.stc_trend_strength > self.state_map.hull_trend_strength:
                self.state_map.combined_trend = self.state_map.stc_trend
            else:
                self.state_map.combined_trend = "NEUTRAL"
        
        # 综合趋势强度
        self.state_map.combined_trend_strength = (self.state_map.hull_trend_strength + self.state_map.stc_trend_strength) / 2
        
        # 综合趋势类型
        self.state_map.combined_trend_type = "STRONG" if self.state_map.combined_trend_strength > self.params_map.strong_trend_threshold else "NORMAL"
    
    def _weighted_moving_average(self, data: List[float], period: int) -> float:
        """计算加权移动平均"""
        if len(data) < period:
            return sum(data) / len(data) if data else 0.0
        
        # 使用最近period个数据点
        recent_data = data[-period:]
        weights = np.arange(1, period + 1)
        values = np.array(recent_data)
        
        return np.sum(weights * values) / np.sum(weights)
    
    def _exponential_moving_average(self, data: List[float], period: int) -> float:
        """计算指数移动平均"""
        if not data:
            return 0.0
        if len(data) == 1:
            return data[0]
        
        alpha = 2.0 / (period + 1)
        ema = data[0]
        for price in data[1:]:
            ema = alpha * price + (1 - alpha) * ema
        
        return ema
    
    def _stochastic_oscillator(self, values: List[float], period: int) -> float:
        """计算随机振荡器"""
        if len(values) < period:
            return 50.0
        
        recent_values = values[-period:]
        highest = max(recent_values)
        lowest = min(recent_values)
        current = values[-1]
        
        if highest == lowest:
            return 50.0
        
        return 100 * (current - lowest) / (highest - lowest)
    
    def _update_intelligent_systems_with_hot_update(self, kline: KLineData) -> None:
        """更新智能化系统（支持热更新）"""
        # 获取当前持仓状态
        position = self.get_position(self.params_map.instrument_id)
        current_position = position.net_position
        
        # 激活/停用预测系统（仅在持仓时激活）
        if current_position != 0:
            self.prediction_system.activate(current_position)
            self.state_map.prediction_active = True
            
            # 计算群论特征
            group_features = self._extract_group_features_for_prediction()
            
            # 实时预测（支持热更新）
            predicted_price, prediction_confidence = self.prediction_system.predict_next_prices(
                kline.close, self.hull_prices, [group_features], self.state_map.kline_count
            )
            
            # 热更新预测状态
            if self.prediction_system.should_update(self.state_map.kline_count):
                self.state_map.predicted_price = predicted_price
                self.state_map.prediction_confidence = prediction_confidence
                self.state_map.prediction_last_update = self.state_map.kline_count
            
            # 计算市场波动率
            market_volatility = self._calculate_market_volatility()
            
            # 自适应风险控制（支持热更新）
            if hasattr(position, 'avg_price') and position.avg_price > 0:
                if self.adaptive_risk_controller.should_update(
                    self.state_map.kline_count, self.params_map.risk_update_frequency
                ):
                    risk_levels = self.adaptive_risk_controller.calculate_adaptive_levels(
                        current_price=kline.close,
                        entry_price=position.avg_price,
                        position_direction=current_position,
                        market_volatility=market_volatility,
                        prediction_confidence=prediction_confidence,
                        hull_trend=self.state_map.hull_trend,
                        stc_trend=self.state_map.stc_trend,
                        hull_trend_strength=self.state_map.hull_trend_strength,
                        stc_trend_strength=self.state_map.stc_trend_strength,
                        current_time=self.state_map.kline_count,
                        update_frequency=self.params_map.risk_update_frequency
                    )
                    
                    # 热更新自适应风险控制状态
                    self.state_map.adaptive_stop_loss = risk_levels['stop_loss_price']
                    self.state_map.adaptive_take_profit = risk_levels['take_profit_price']
                    self.state_map.risk_level = risk_levels['risk_level']
                    self.state_map.risk_last_update = self.state_map.kline_count
        else:
            # 无持仓时停用预测系统并清空状态
            self.prediction_system.deactivate()
            self.state_map.prediction_active = False
            self.state_map.predicted_price = 0
            self.state_map.prediction_confidence = 0
            self.state_map.adaptive_stop_loss = 0
            self.state_map.adaptive_take_profit = 0
            self.state_map.risk_level = 0
    
    def _extract_group_features_for_prediction(self) -> Dict:
        """为预测系统提取群论特征"""
        return {
            'symmetry_factor': self._calculate_price_symmetry(),
            'invariant_factor': self._calculate_group_invariants(),
            'lie_group_factor': self._apply_lie_group_transformation(0.0),
            'permutation_factor': self._analyze_permutation_patterns()
        }
    
    def _calculate_market_volatility(self) -> float:
        """计算市场波动率"""
        if len(self.hull_prices) < 10:
            return 0.02  # 默认波动率
        
        recent_prices = np.array(self.hull_prices[-10:])
        returns = np.diff(recent_prices) / recent_prices[:-1]
        volatility = np.std(returns) if len(returns) > 1 else 0.02
        
        return volatility
    
    def _apply_group_theory_enhancement(self, signal_strength: float, confidence: float) -> Tuple[float, float]:
        """应用群论数学增强 - 内嵌群论运算"""
        
        # 群论对称性分析
        symmetry_factor = self._calculate_price_symmetry()
        
        # 群不变量计算
        invariant_factor = self._calculate_group_invariants()
        
        # 李群变换增强
        lie_group_factor = self._apply_lie_group_transformation(signal_strength)
        
        # 置换群模式识别
        permutation_factor = self._analyze_permutation_patterns()
        
        # 综合群论增强因子
        group_enhancement = (
            0.3 * symmetry_factor + 
            0.3 * invariant_factor + 
            0.2 * lie_group_factor + 
            0.2 * permutation_factor
        )
        
        # 应用群论增强
        enhanced_signal = signal_strength * (1 + group_enhancement * 0.2)
        enhanced_confidence = confidence * (1 + abs(group_enhancement) * 0.1)
        
        # 限制在合理范围内
        enhanced_signal = max(-1.0, min(1.0, enhanced_signal))
        enhanced_confidence = max(0.0, min(1.0, enhanced_confidence))
        
        return enhanced_signal, enhanced_confidence
    
    def _calculate_price_symmetry(self) -> float:
        """计算价格序列的对称性 - 群论对称性分析"""
        if len(self.hull_prices) < 10:
            return 0.0
        
        # 使用最近的价格数据
        recent_prices = self.hull_prices[-10:]
        
        # 计算价格序列的对称性度量
        center = len(recent_prices) // 2
        left_part = recent_prices[:center]
        right_part = recent_prices[center:][::-1]  # 反转右半部分
        
        if len(left_part) == len(right_part):
            # 计算对称性相关系数
            if len(left_part) > 1:
                correlation = np.corrcoef(left_part, right_part)[0, 1]
                return correlation if not np.isnan(correlation) else 0.0
        
        return 0.0
    
    def _calculate_group_invariants(self) -> float:
        """计算群不变量 - 价格序列的本质特征"""
        if len(self.hull_prices) < 5:
            return 0.0
        
        recent_prices = np.array(self.hull_prices[-5:])
        
        # 计算多项式不变量
        # 一次不变量：平均值
        inv1 = np.mean(recent_prices)
        
        # 二次不变量：方差
        inv2 = np.var(recent_prices)
        
        # 三次不变量：偏度相关
        mean_val = np.mean(recent_prices)
        inv3 = np.mean((recent_prices - mean_val) ** 3)
        
        # 标准化不变量
        price_scale = np.mean(recent_prices) if np.mean(recent_prices) > 0 else 1.0
        normalized_inv2 = inv2 / (price_scale ** 2)
        normalized_inv3 = inv3 / (price_scale ** 3)
        
        # 综合不变量因子
        invariant_factor = np.tanh(normalized_inv2 + abs(normalized_inv3))
        
        return invariant_factor
    
    def _apply_lie_group_transformation(self, signal: float) -> float:
        """应用李群变换 - 连续变换群分析"""
        if len(self.hull_prices) < 3:
            return 0.0
        
        # 计算价格变化的李群表示
        recent_prices = np.array(self.hull_prices[-3:])
        
        # 计算对数变化率（李代数元素）
        if recent_prices[0] > 0 and recent_prices[1] > 0:
            log_change1 = math.log(recent_prices[1] / recent_prices[0])
            log_change2 = math.log(recent_prices[2] / recent_prices[1])
            
            # 李群指数映射近似
            exp_factor = math.exp(log_change1 + log_change2) - 1
            
            # 应用到信号强度
            lie_factor = np.tanh(exp_factor * 10)  # 标准化
            
            return lie_factor
        
        return 0.0
    
    def _analyze_permutation_patterns(self) -> float:
        """分析置换群模式 - 价格序列模式识别"""
        if len(self.hull_prices) < 6:
            return 0.0
        
        recent_prices = self.hull_prices[-6:]
        
        # 计算价格序列的排列模式
        # 将价格转换为排名
        sorted_indices = np.argsort(recent_prices)
        ranks = np.empty_like(sorted_indices)
        ranks[sorted_indices] = np.arange(len(recent_prices))
        
        # 计算置换的循环结构
        cycles = self._find_permutation_cycles(ranks)
        
        # 基于循环结构计算模式强度
        if cycles:
            # 较长的循环表示更强的模式
            max_cycle_length = max(len(cycle) for cycle in cycles)
            pattern_strength = max_cycle_length / len(recent_prices)
            
            return pattern_strength
        
        return 0.0
    
    def _find_permutation_cycles(self, permutation: np.ndarray) -> List[List[int]]:
        """寻找置换的循环结构"""
        n = len(permutation)
        visited = [False] * n
        cycles = []
        
        for i in range(n):
            if not visited[i]:
                cycle = []
                j = i
                while not visited[j]:
                    visited[j] = True
                    cycle.append(j)
                    j = permutation[j]
                
                if len(cycle) > 1:
                    cycles.append(cycle)
        
        return cycles  
  
    def calc_signal(self, kline: KLineData):
        """计算交易信号（增强版本 - 支持强趋势/普通趋势判断）"""
        
        # 重置所有信号
        self.buy_signal = False
        self.sell_signal = False
        self.short_signal = False
        self.cover_signal = False
        
        # 检查技术指标是否有效
        if (self.state_map.hull_value == 0 or 
            self.state_map.stc_value == 0 or 
            len(self.hull_prices) < self.params_map.hull_period or
            len(self.stc_prices) < self.params_map.stc_slow):
            self.state_map.trading_action = "WAIT"
            return
        
        # 1. HULL+STC联合信号确认（增强版 - 支持趋势强度）
        hull_stc_signal = self._generate_enhanced_hull_stc_combined_signal()
        
        if hull_stc_signal == "NONE":
            self.state_map.trading_action = "WAIT"
            return
        
        # 2. 群论增强的模糊推理（支持趋势类型）
        fuzzy_signal, fuzzy_confidence, fuzzy_action = self._enhanced_fuzzy_inference_with_trend_type(hull_stc_signal, kline)
        
        # 3. 斐波拉契三目标预测增强（仅持仓时）
        if self.state_map.prediction_active:
            fibonacci_targets = self._fibonacci_three_target_prediction(kline.close)
            fuzzy_signal, fuzzy_confidence = self._apply_fibonacci_enhancement(
                fuzzy_signal, fuzzy_confidence, fibonacci_targets
            )
        
        # 更新状态
        self.state_map.fuzzy_signal = fuzzy_signal
        self.state_map.fuzzy_confidence = fuzzy_confidence
        self.state_map.fuzzy_action = fuzzy_action
        self.state_map.final_signal = fuzzy_signal
        self.state_map.signal_confidence = fuzzy_confidence
        
        # 4. 智能化交易决策（增强版）
        self._enhanced_intelligent_trading_decision(fuzzy_signal, fuzzy_confidence, kline)
        
        # 设置交易价格
        self.long_price = self.short_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2
        
        # 根据交易方向调整信号
        if self.params_map.trade_direction == "sell":
            self.buy_signal, self.short_signal = self.short_signal, self.buy_signal
        
        self.sell_signal = self.short_signal
        self.cover_signal = self.buy_signal
    
    def _generate_enhanced_hull_stc_combined_signal(self) -> str:
        """生成增强的HULL+STC联合确认信号（支持趋势强度判断）"""
        
        # HULL趋势状态
        hull_bullish = self.state_map.hull_trend == "BULLISH"
        hull_bearish = self.state_map.hull_trend == "BEARISH"
        hull_strong = self.state_map.hull_trend_type == "STRONG"
        
        # STC动量状态
        stc_bullish = self.state_map.stc_trend == "BULLISH"
        stc_bearish = self.state_map.stc_trend == "BEARISH"
        stc_strong = self.state_map.stc_trend_type == "STRONG"
        
        # STC数值位置判断
        stc_oversold = self.state_map.stc_value < 25
        stc_overbought = self.state_map.stc_value > 75
        stc_neutral_zone = 25 <= self.state_map.stc_value <= 75
        
        # 综合趋势强度
        combined_strong = self.state_map.combined_trend_type == "STRONG"
        
        # 超强买入信号：双强趋势 + 技术位置配合
        if hull_bullish and stc_bullish and hull_strong and stc_strong and (stc_oversold or stc_neutral_zone):
            return "ULTRA_STRONG_BUY"
        
        # 强烈买入信号：强趋势 + 技术位置配合
        if hull_bullish and stc_bullish and (hull_strong or stc_strong) and (stc_oversold or stc_neutral_zone):
            return "STRONG_BUY"
        
        # 买入信号：普通趋势一致
        if hull_bullish and stc_bullish and stc_neutral_zone:
            return "BUY"
        
        # 超强卖出信号：双强趋势 + 技术位置配合
        if hull_bearish and stc_bearish and hull_strong and stc_strong and (stc_overbought or stc_neutral_zone):
            return "ULTRA_STRONG_SELL"
        
        # 强烈卖出信号：强趋势 + 技术位置配合
        if hull_bearish and stc_bearish and (hull_strong or stc_strong) and (stc_overbought or stc_neutral_zone):
            return "STRONG_SELL"
        
        # 卖出信号：普通趋势一致
        if hull_bearish and stc_bearish and stc_neutral_zone:
            return "SELL"
        
        # 强趋势反转信号：强趋势背离
        if hull_bullish and stc_bearish and hull_strong and stc_overbought:
            return "STRONG_REVERSAL_SELL"
        
        if hull_bearish and stc_bullish and hull_strong and stc_oversold:
            return "STRONG_REVERSAL_BUY"
        
        # 普通反转信号：趋势背离
        if hull_bullish and stc_bearish and stc_overbought:
            return "REVERSAL_SELL"
        
        if hull_bearish and stc_bullish and stc_oversold:
            return "REVERSAL_BUY"
        
        # 无明确信号
        return "NONE"
    
    def _enhanced_fuzzy_inference_with_trend_type(self, hull_stc_signal: str, kline: KLineData) -> Tuple[float, float, str]:
        """增强的模糊推理（支持趋势类型判断）"""
        
        # 信号强度映射（增强版）
        signal_mapping = {
            "ULTRA_STRONG_BUY": (0.95, 0.95, "strong_buy"),
            "STRONG_BUY": (0.8, 0.9, "strong_buy"),
            "BUY": (0.4, 0.7, "buy"),
            "STRONG_REVERSAL_BUY": (0.5, 0.8, "buy"),
            "REVERSAL_BUY": (0.3, 0.6, "buy"),
            "ULTRA_STRONG_SELL": (-0.95, 0.95, "strong_sell"),
            "STRONG_SELL": (-0.8, 0.9, "strong_sell"),
            "SELL": (-0.4, 0.7, "sell"),
            "STRONG_REVERSAL_SELL": (-0.5, 0.8, "sell"),
            "REVERSAL_SELL": (-0.3, 0.6, "sell"),
            "NONE": (0.0, 0.0, "hold")
        }
        
        base_signal, base_confidence, action = signal_mapping.get(hull_stc_signal, (0.0, 0.0, "hold"))
        
        # 趋势强度加权
        trend_strength_factor = 1.0 + self.state_map.combined_trend_strength * 0.3
        
        # 趋势类型加权
        if self.state_map.combined_trend_type == "STRONG":
            trend_type_factor = 1.2
        else:
            trend_type_factor = 1.0
        
        # 应用加权
        weighted_signal = base_signal * trend_strength_factor * trend_type_factor
        weighted_confidence = base_confidence * trend_strength_factor
        
        # 群论数学增强
        enhanced_signal, enhanced_confidence = self._apply_group_theory_enhancement(weighted_signal, weighted_confidence)
        
        return enhanced_signal, enhanced_confidence, action
    
    def _fibonacci_three_target_prediction(self, current_price: float) -> Dict:
        """斐波拉契三目标预测系统（增强版）"""
        if len(self.hull_prices) < 20:
            return {"target1": current_price, "target2": current_price, "target3": current_price, 
                   "confidence1": 0.0, "confidence2": 0.0, "confidence3": 0.0}
        
        # 计算价格波动范围
        recent_prices = np.array(self.hull_prices[-20:])
        price_high = np.max(recent_prices)
        price_low = np.min(recent_prices)
        price_range = price_high - price_low
        
        # 斐波拉契回调/扩展比例
        fib_ratios = [0.236, 0.382, 0.618]
        
        # 判断当前趋势方向（基于综合趋势）
        if self.state_map.combined_trend == "BULLISH":
            trend_direction = 1
        elif self.state_map.combined_trend == "BEARISH":
            trend_direction = -1
        else:
            trend_direction = 0
        
        # 计算三个目标价位
        if trend_direction > 0:  # 上升趋势
            target1 = current_price + price_range * fib_ratios[0]  # 23.6%目标
            target2 = current_price + price_range * fib_ratios[1]  # 38.2%目标
            target3 = current_price + price_range * fib_ratios[2]  # 61.8%目标
        elif trend_direction < 0:  # 下降趋势
            target1 = current_price - price_range * fib_ratios[0]  # 23.6%目标
            target2 = current_price - price_range * fib_ratios[1]  # 38.2%目标
            target3 = current_price - price_range * fib_ratios[2]  # 61.8%目标
        else:  # 中性趋势
            target1 = target2 = target3 = current_price
        
        # 计算各目标的置信度（基于趋势强度）
        market_volatility = self._calculate_market_volatility()
        base_confidence = 1.0 / (1.0 + market_volatility * 10)
        
        # 趋势强度加权
        strength_factor = 0.5 + self.state_map.combined_trend_strength * 0.5
        
        # 距离越近，置信度越高
        confidence1 = base_confidence * strength_factor * 0.9  # 最近目标，最高置信度
        confidence2 = base_confidence * strength_factor * 0.7  # 中等目标，中等置信度
        confidence3 = base_confidence * strength_factor * 0.5  # 最远目标，最低置信度
        
        # 基于群论特征调整置信度
        group_features = self._extract_group_features_for_prediction()
        symmetry_factor = group_features.get('symmetry_factor', 0)
        
        # 对称性强时，提高置信度
        if abs(symmetry_factor) > 0.5:
            confidence1 *= 1.2
            confidence2 *= 1.1
            confidence3 *= 1.05
        
        return {
            "target1": target1,
            "target2": target2, 
            "target3": target3,
            "confidence1": min(1.0, confidence1),
            "confidence2": min(1.0, confidence2),
            "confidence3": min(1.0, confidence3),
            "trend_direction": trend_direction
        }
    
    def _apply_fibonacci_enhancement(self, signal: float, confidence: float, fibonacci_targets: Dict) -> Tuple[float, float]:
        """应用斐波拉契预测增强（增强版）"""
        if not self.state_map.prediction_active:
            return signal, confidence
        
        # 获取最高置信度的目标
        max_confidence = max(fibonacci_targets["confidence1"], 
                           fibonacci_targets["confidence2"], 
                           fibonacci_targets["confidence3"])
        
        # 基于斐波拉契预测调整信号强度
        if max_confidence > 0.6:  # 降低阈值，更容易触发增强
            # 高置信度预测，增强信号
            trend_direction = fibonacci_targets["trend_direction"]
            if (signal > 0 and trend_direction > 0) or (signal < 0 and trend_direction < 0):
                # 信号与预测方向一致，增强
                enhancement_factor = 1 + max_confidence * 0.4  # 增加增强幅度
                enhanced_signal = signal * enhancement_factor
                enhanced_confidence = confidence * (1 + max_confidence * 0.3)
            else:
                # 信号与预测方向不一致，减弱
                reduction_factor = 1 - max_confidence * 0.3
                enhanced_signal = signal * reduction_factor
                enhanced_confidence = confidence * (1 - max_confidence * 0.2)
        else:
            enhanced_signal = signal
            enhanced_confidence = confidence
        
        # 更新预测状态
        self.state_map.predicted_price = fibonacci_targets["target1"]  # 使用最近的目标作为预测价格
        
        # 限制范围
        enhanced_signal = max(-1.0, min(1.0, enhanced_signal))
        enhanced_confidence = max(0.0, min(1.0, enhanced_confidence))
        
        return enhanced_signal, enhanced_confidence
    
    def _enhanced_intelligent_trading_decision(self, fuzzy_signal: float, fuzzy_confidence: float, kline: KLineData):
        """增强的智能化交易决策（支持趋势强度判断）"""
        
        # 1. 信号质量验证
        if not self._validate_enhanced_signal_quality():
            self.state_map.trading_action = "WAIT"
            self.buy_signal = False
            self.sell_signal = False
            self.short_signal = False
            self.cover_signal = False
            self._log_trading_decision("WAIT", "信号质量不足")
            return
        
        # 2. 基础阈值设置
        base_threshold = self.params_map.signal_threshold
        base_confidence_threshold = self.params_map.confidence_threshold
        
        # 3. 智能化阈值调整（增强版）
        adjusted_threshold = base_threshold
        adjusted_confidence_threshold = base_confidence_threshold
        
        # 根据预测置信度调整阈值（仅持仓时）
        if self.state_map.prediction_active and self.state_map.prediction_confidence > 0:
            prediction_factor = 1.0 - self.state_map.prediction_confidence * 0.3  # 增加调整幅度
            adjusted_threshold *= prediction_factor
            adjusted_confidence_threshold *= (1.0 - self.state_map.prediction_confidence * 0.15)
        
        # 根据市场波动率调整阈值
        market_volatility = self._calculate_market_volatility()
        volatility_factor = 1.0 + market_volatility * 2.5  # 减少波动率影响
        adjusted_threshold *= volatility_factor
        
        # 根据综合趋势强度调整阈值（新增）
        if self.state_map.combined_trend_type == "STRONG":
            # 强趋势时降低阈值（更容易触发信号）
            trend_factor = 0.7
        else:
            # 普通趋势时保持阈值
            trend_factor = 1.0
        
        adjusted_threshold *= trend_factor
        adjusted_confidence_threshold *= (0.8 + trend_factor * 0.2)
        
        # 根据技术指标一致性调整阈值
        if self.state_map.hull_trend == self.state_map.stc_trend and self.state_map.hull_trend != "NEUTRAL":
            # 趋势一致时降低阈值（更容易触发信号）
            consistency_factor = 0.75  # 进一步降低
        elif self.state_map.hull_trend != self.state_map.stc_trend:
            # 趋势不一致时提高阈值（更难触发信号）
            consistency_factor = 1.3
        else:
            consistency_factor = 1.0
        
        adjusted_threshold *= consistency_factor
        adjusted_confidence_threshold *= (0.9 + consistency_factor * 0.1)
        
        # 4. 交易决策逻辑（增强版）
        if fuzzy_confidence > adjusted_confidence_threshold:
            if fuzzy_signal > adjusted_threshold:
                self.state_map.trading_action = "BUY"
                self.buy_signal = True
                self.short_signal = False
                self._log_trading_decision("BUY", f"信号强度: {fuzzy_signal:.3f}, 置信度: {fuzzy_confidence:.3f}, 趋势类型: {self.state_map.combined_trend_type}")
                
            elif fuzzy_signal < -adjusted_threshold:
                self.state_map.trading_action = "SELL"
                self.buy_signal = False
                self.short_signal = True
                self._log_trading_decision("SELL", f"信号强度: {fuzzy_signal:.3f}, 置信度: {fuzzy_confidence:.3f}, 趋势类型: {self.state_map.combined_trend_type}")
                
            else:
                self.state_map.trading_action = "HOLD"
                self.buy_signal = False
                self.short_signal = False
                self._log_trading_decision("HOLD", "信号强度不足")
        else:
            self.state_map.trading_action = "WAIT"
            self.buy_signal = False
            self.short_signal = False
            self._log_trading_decision("WAIT", f"置信度不足: {fuzzy_confidence:.3f} < {adjusted_confidence_threshold:.3f}")
        
        # 5. 更新性能指标
        self._update_performance_metrics()
    
    def _validate_enhanced_signal_quality(self) -> bool:
        """验证增强的信号质量"""
        # 1. 基础数据完整性检查
        if (len(self.hull_prices) < self.params_map.hull_period or
            len(self.stc_prices) < self.params_map.stc_slow):
            return False
        
        # 2. 趋势强度检查
        if (self.state_map.hull_trend_strength < 0.1 and 
            self.state_map.stc_trend_strength < 0.1):
            return False
        
        # 3. 技术指标有效性检查
        if (self.state_map.hull_value == 0 or 
            self.state_map.stc_value == 0):
            return False
        
        # 4. 市场波动率检查
        market_volatility = self._calculate_market_volatility()
        if market_volatility > 0.15:  # 极端波动时暂停交易
            return False
        
        # 5. 综合趋势一致性检查
        if self.state_map.combined_trend == "NEUTRAL" and self.state_map.combined_trend_strength < 0.3:
            return False
        
        return True
    
    def _log_trading_decision(self, action: str, reason: str):
        """记录交易决策日志"""
        print(f"🎯 交易决策: {action} - {reason}")
        print(f"   HULL趋势: {self.state_map.hull_trend}({self.state_map.hull_trend_type}) 强度: {self.state_map.hull_trend_strength:.2f}")
        print(f"   STC趋势: {self.state_map.stc_trend}({self.state_map.stc_trend_type}) 强度: {self.state_map.stc_trend_strength:.2f}")
        print(f"   综合趋势: {self.state_map.combined_trend}({self.state_map.combined_trend_type}) 强度: {self.state_map.combined_trend_strength:.2f}")
        if self.state_map.prediction_active:
            print(f"   预测价格: {self.state_map.predicted_price:.2f} 置信度: {self.state_map.prediction_confidence:.2f}")
            print(f"   风险水平: {self.state_map.risk_level:.2f}")
    
    def _update_performance_metrics(self):
        """更新性能指标"""
        # 这里可以添加性能指标的更新逻辑
        pass
    
    def exec_signal(self):
        """执行交易信号（增强版本）"""
        self.signal_price = 0
        
        position = self.get_position(self.params_map.instrument_id)
        current_price = self.tick.last_price if self.tick else (self.long_price or self.short_price)
        
        if self.order_id is not None:
            # 挂单未成交，撤单
            self.cancel_order(self.order_id)
        
        # 1. 兜底性强平检查（最高优先级）
        emergency_close = self._check_emergency_force_close(position, current_price)
        if emergency_close:
            self._execute_emergency_force_close(position, current_price)
            return
        
        # 2. 智能化自适应止盈止损检查（热更新支持）
        if position.net_position != 0:
            should_close = self._check_adaptive_stop_levels(position, current_price)
            if should_close:
                self._execute_full_position_close(position, current_price, "自适应止盈止损")
                return
        
        # 3. 平仓逻辑（信号驱动 - 必须全部平仓）
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self._execute_full_position_close(position, self.short_price, "信号平多")
                
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            if self.trading:
                self._execute_full_position_close(position, self.long_price, "信号平空")
        
        # 4. 开仓逻辑（动态仓位管理 - 增强版）
        elif position.net_position == 0:  # 仅在无持仓时开仓
            if self.short_signal:
                self.signal_price = -self.short_price
                if self.trading:
                    adjusted_volume = self._calculate_enhanced_dynamic_position_size("SELL")
                    self.order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=adjusted_volume,
                        price=self.short_price,
                        order_direction="sell"
                    )
                    
            elif self.buy_signal:
                self.signal_price = self.long_price
                if self.trading:
                    adjusted_volume = self._calculate_enhanced_dynamic_position_size("BUY")
                    self.order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=adjusted_volume,
                        price=self.long_price,
                        order_direction="buy"
                    )
    
    def _check_emergency_force_close(self, position, current_price) -> bool:
        """检查兜底性强平条件（增强版）"""
        if position.net_position == 0:
            return False
        
        # 1. 极端亏损检查（超过5%强制平仓）
        if hasattr(position, 'avg_price') and position.avg_price > 0:
            if position.net_position > 0:  # 多头
                loss_ratio = (position.avg_price - current_price) / position.avg_price
            else:  # 空头
                loss_ratio = (current_price - position.avg_price) / position.avg_price
            
            if loss_ratio > 0.05:  # 亏损超过5%
                return True
        
        # 2. 风险水平过高检查
        if self.state_map.risk_level > 1.5:  # 风险水平超过1.5倍
            return True
        
        # 3. 市场极端波动检查
        market_volatility = self._calculate_market_volatility()
        if market_volatility > 0.12:  # 波动率超过12%
            return True
        
        # 4. 技术指标极端背离检查（增强版）
        if (self.state_map.hull_trend == "BULLISH" and self.state_map.stc_trend == "BEARISH" and 
            self.state_map.stc_value > 85 and position.net_position > 0 and
            self.state_map.hull_trend_type == "STRONG"):
            return True
        
        if (self.state_map.hull_trend == "BEARISH" and self.state_map.stc_trend == "BULLISH" and 
            self.state_map.stc_value < 15 and position.net_position < 0 and
            self.state_map.hull_trend_type == "STRONG"):
            return True
        
        # 5. 综合趋势反转检查（新增）
        if (self.state_map.combined_trend_type == "STRONG" and 
            ((self.state_map.combined_trend == "BEARISH" and position.net_position > 0) or
             (self.state_map.combined_trend == "BULLISH" and position.net_position < 0))):
            return True
        
        return False
    
    def _execute_emergency_force_close(self, position, current_price):
        """执行兜底性强平（全部平仓）"""
        print(f"🚨 触发兜底性强平！持仓: {position.net_position}, 价格: {current_price}")
        print(f"   综合趋势: {self.state_map.combined_trend}({self.state_map.combined_trend_type})")
        print(f"   风险水平: {self.state_map.risk_level:.2f}")
        
        if position.net_position > 0:
            # 强平多头
            self.signal_price = -current_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=current_price,
                    volume=position.net_position,  # 全部平仓
                    order_direction="sell"
                )
        elif position.net_position < 0:
            # 强平空头
            self.signal_price = current_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=current_price,
                    volume=abs(position.net_position),  # 全部平仓
                    order_direction="buy"
                )
    
    def _execute_full_position_close(self, position, price, reason):
        """执行全仓位平仓（避免部分平仓）"""
        if position.net_position == 0:
            return
        
        print(f"📤 执行全仓平仓 - {reason}: 持仓 {position.net_position}, 价格 {price}")
        print(f"   综合趋势: {self.state_map.combined_trend}({self.state_map.combined_trend_type})")
        
        if position.net_position > 0:
            # 平多头全部持仓
            self.order_id = self.auto_close_position(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                price=price,
                volume=position.net_position,  # 全部持仓
                order_direction="sell"
            )
        elif position.net_position < 0:
            # 平空头全部持仓
            self.order_id = self.auto_close_position(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                price=price,
                volume=abs(position.net_position),  # 全部持仓
                order_direction="buy"
            )
    
    def _calculate_enhanced_dynamic_position_size(self, direction: str) -> int:
        """计算增强的动态仓位大小（基于趋势强度）"""
        base_volume = self.params_map.order_volume
        
        # 1. 基于信号强度调整
        signal_strength = abs(self.state_map.final_signal)
        signal_factor = 0.5 + signal_strength * 0.5  # 0.5-1.0倍
        
        # 2. 基于市场波动率调整
        market_volatility = self._calculate_market_volatility()
        volatility_factor = 1.0 / (1.0 + market_volatility * 8)  # 减少波动率影响
        
        # 3. 基于预测置信度调整（仅持仓时有效）
        confidence_factor = 1.0
        if self.state_map.prediction_active:
            confidence_factor = 0.5 + self.state_map.prediction_confidence * 0.5
        
        # 4. 基于综合趋势强度调整（新增）
        trend_strength_factor = 0.7 + self.state_map.combined_trend_strength * 0.6
        
        # 5. 基于趋势类型调整（新增）
        if self.state_map.combined_trend_type == "STRONG":
            trend_type_factor = 1.3  # 强趋势时增加仓位
        else:
            trend_type_factor = 1.0
        
        # 6. 基于技术指标一致性调整
        consistency_factor = 1.0
        if self.state_map.hull_trend == self.state_map.stc_trend and self.state_map.hull_trend != "NEUTRAL":
            consistency_factor = 1.2  # 趋势一致时增加仓位
        elif self.state_map.hull_trend != self.state_map.stc_trend:
            consistency_factor = 0.8  # 趋势不一致时减少仓位
        
        # 综合调整因子
        total_factor = (signal_factor * volatility_factor * confidence_factor * 
                       trend_strength_factor * trend_type_factor * consistency_factor)
        
        # 计算最终仓位
        adjusted_volume = int(base_volume * total_factor)
        
        # 限制仓位范围
        min_volume = max(1, base_volume // 2)  # 最小不低于基础仓位的一半
        max_volume = base_volume * 3  # 最大不超过基础仓位的3倍（增加上限）
        
        final_volume = max(min_volume, min(max_volume, adjusted_volume))
        
        print(f"📊 增强动态仓位计算 - 方向: {direction}, 基础: {base_volume}")
        print(f"   信号强度: {signal_strength:.2f}, 波动率: {market_volatility:.4f}")
        print(f"   趋势强度: {self.state_map.combined_trend_strength:.2f}, 趋势类型: {self.state_map.combined_trend_type}")
        print(f"   最终仓位: {final_volume}")
        
        return final_volume
    
    def _check_adaptive_stop_levels(self, position, current_price) -> bool:
        """检查自适应止盈止损水平（热更新支持）"""
        if not self.params_map.adaptive_risk_enabled:
            return False
        
        if self.state_map.adaptive_stop_loss == 0 or self.state_map.adaptive_take_profit == 0:
            return False
        
        # 多头持仓检查
        if position.net_position > 0:
            # 止损检查
            if current_price <= self.state_map.adaptive_stop_loss:
                return True
            # 止盈检查
            if current_price >= self.state_map.adaptive_take_profit:
                return True
        
        # 空头持仓检查
        elif position.net_position < 0:
            # 止损检查
            if current_price >= self.state_map.adaptive_stop_loss:
                return True
            # 止盈检查
            if current_price <= self.state_map.adaptive_take_profit:
                return True
        
        return False


# 策略实例化
strategy = Strategy1Enhanced()