from typing import Literal, Dict, Any
import logging
import numpy as np
import math
from datetime import datetime, timedelta

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型 - 层次化智能决策版本"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="D1", title="K线周期")
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 维加斯隧道核心参数
    vegas_fast: int = Field(default=144, title="维加斯快线周期")
    vegas_slow: int = Field(default=169, title="维加斯慢线周期")
    
    # 多时间框架确认系统
    confirm_timeframe: str = Field(default="H4", title="确认时间框架")
    tunnel_width_threshold: float = Field(default=0.005, title="隧道宽度阈值(百分比)")
    
    # 多重EMA系统
    ema_fast: int = Field(default=12, title="快速EMA周期")
    ema_medium: int = Field(default=26, title="中速EMA周期") 
    ema_slow: int = Field(default=50, title="慢速EMA周期")
    
    # MACD参数
    macd_fast: int = Field(default=12, title="MACD快线")
    macd_slow: int = Field(default=26, title="MACD慢线")
    macd_signal: int = Field(default=9, title="MACD信号线")
    
    # RSI参数
    rsi_period: int = Field(default=14, title="RSI周期")
    rsi_overbought: float = Field(default=70, title="RSI超买线")
    rsi_oversold: float = Field(default=30, title="RSI超卖线")
    
    # 布林带参数
    bb_period: int = Field(default=20, title="布林带周期")
    bb_std: float = Field(default=2.0, title="布林带标准差倍数")
    
    # 成交量确认
    volume_ma_period: int = Field(default=20, title="成交量均线周期")
    volume_threshold: float = Field(default=1.2, title="成交量放大倍数")
    avg_volume_period: int = Field(default=30, title="平均成交量周期")
    
    # ADX趋势强度
    adx_period: int = Field(default=14, title="ADX周期")
    adx_threshold: float = Field(default=25, title="ADX趋势阈值")
    
    # ATR参数
    atr_period: int = Field(default=14, title="ATR周期")
    
    # 层次化决策阈值
    major_premise_threshold: float = Field(default=0.7, title="大前提确认阈值")
    minor_premise_threshold: float = Field(default=0.6, title="小前提确认阈值")
    precondition_threshold: float = Field(default=0.5, title="前置条件阈值")
    
    # 追踪止盈止损参数
    trailing_stop_atr_multiplier: float = Field(default=2.0, title="追踪止损ATR倍数")
    trailing_profit_atr_multiplier: float = Field(default=3.0, title="追踪止盈ATR倍数")
    trailing_activation_ratio: float = Field(default=0.02, title="追踪激活比例(2%)")


class State(BaseState):
    """状态映射模型 - 层次化智能决策版本"""
    # 维加斯隧道核心指标
    vegas_fast: float = Field(default=0, title="维加斯快线")
    vegas_slow: float = Field(default=0, title="维加斯慢线")
    
    # 多时间框架确认
    tunnel_strength: float = Field(default=0, title="隧道强度")
    tunnel_width: float = Field(default=0, title="隧道宽度")
    
    # 价格隧道位置能量指标
    price_energy: float = Field(default=0, title="价格能量")
    avg_volume: float = Field(default=0, title="平均成交量")
    
    # 多重EMA系统
    ema_fast: float = Field(default=0, title="快速EMA")
    ema_medium: float = Field(default=0, title="中速EMA")
    ema_slow: float = Field(default=0, title="慢速EMA")
    
    # MACD指标
    macd_line: float = Field(default=0, title="MACD线")
    macd_signal: float = Field(default=0, title="MACD信号线")
    macd_histogram: float = Field(default=0, title="MACD柱状图")
    
    # RSI指标
    rsi: float = Field(default=50, title="RSI值")
    
    # 布林带指标
    bb_upper: float = Field(default=0, title="布林带上轨")
    bb_middle: float = Field(default=0, title="布林带中轨")
    bb_lower: float = Field(default=0, title="布林带下轨")
    bb_percent: float = Field(default=0, title="布林带百分比")
    
    # 成交量指标
    volume_ma: float = Field(default=0, title="成交量均线")
    volume_ratio: float = Field(default=1, title="成交量比率")
    
    # ADX趋势强度
    adx: float = Field(default=0, title="ADX值")
    di_plus: float = Field(default=0, title="DI+")
    di_minus: float = Field(default=0, title="DI-")
    
    # ATR
    atr: float = Field(default=0, title="ATR值")
    
    # 层次化决策状态
    major_premise_score: float = Field(default=0, title="大前提得分")
    minor_premise_score: float = Field(default=0, title="小前提得分")
    precondition_score: float = Field(default=0, title="前置条件得分")
    final_direction: int = Field(default=0, title="最终方向(-1/0/1)")
    
    # 市场状态感知
    market_state: Dict[str, float] = Field(default_factory=dict, title="市场状态")
    trend_score: float = Field(default=0, title="趋势得分")
    volatility_score: float = Field(default=0, title="波动率得分")
    momentum_score: float = Field(default=0, title="动量得分")
    composite_state: float = Field(default=0, title="综合市场状态")
    
    # 订单管理
    pending_order: bool = Field(default=False, title="是否有挂单")
    kline_count: int = Field(default=0, title="K线计数")
    
    # 历史数据
    high_prices: list = Field(default_factory=list, title="最高价历史")
    low_prices: list = Field(default_factory=list, title="最低价历史")
    close_prices: list = Field(default_factory=list, title="收盘价历史")
    open_prices: list = Field(default_factory=list, title="开盘价历史")
    volume_data: list = Field(default_factory=list, title="成交量历史")
    
    # 多时间框架数据缓存
    confirm_tf_data: list = Field(default_factory=list, title="确认时间框架数据")


class TrailingStopProfit:
    """追踪止盈止损管理器"""
    
    def __init__(self, logger):
        self.logger = logger
        self.reset()
    
    def reset(self):
        """重置追踪状态"""
        self.is_active = False
        self.position_direction = 0  # 1=多头, -1=空头, 0=无持仓
        self.entry_price = 0.0
        self.best_price = 0.0  # 最优价格(多头最高价/空头最低价)
        self.trailing_stop_price = 0.0  # 追踪止损价
        self.trailing_profit_price = 0.0  # 追踪止盈价
        self.activation_threshold = 0.0  # 激活阈值
        self.is_activated = False  # 是否已激活追踪
    
    def initialize_position(self, direction: int, entry_price: float, atr: float, 
                          activation_ratio: float, stop_multiplier: float, profit_multiplier: float):
        """初始化持仓追踪参数"""
        self.is_active = True
        self.position_direction = direction
        self.entry_price = entry_price
        self.best_price = entry_price
        self.is_activated = False
        
        # 计算激活阈值(入场价格的2%)
        self.activation_threshold = entry_price * activation_ratio
        
        # 初始止损止盈线
        if direction > 0:  # 多头
            self.trailing_stop_price = entry_price - atr * stop_multiplier
            self.trailing_profit_price = entry_price + atr * profit_multiplier
        else:  # 空头
            self.trailing_stop_price = entry_price + atr * stop_multiplier
            self.trailing_profit_price = entry_price - atr * profit_multiplier
        
        self.logger.info(f"追踪系统初始化: 方向={'多头' if direction > 0 else '空头'}, "
                        f"入场价={entry_price:.4f}, 止损={self.trailing_stop_price:.4f}, "
                        f"止盈={self.trailing_profit_price:.4f}, 激活阈值={self.activation_threshold:.4f}")
    
    def update(self, current_price: float, atr: float, stop_multiplier: float, profit_multiplier: float):
        """更新追踪止盈止损"""
        if not self.is_active or self.position_direction == 0:
            return None
        
        # 检查是否达到激活条件
        if not self.is_activated:
            if self.position_direction > 0:  # 多头
                if current_price >= (self.entry_price + self.activation_threshold):
                    self.is_activated = True
                    self.logger.info(f"多头追踪激活: 当前价={current_price:.4f}, 激活价={self.entry_price + self.activation_threshold:.4f}")
            else:  # 空头
                if current_price <= (self.entry_price - self.activation_threshold):
                    self.is_activated = True
                    self.logger.info(f"空头追踪激活: 当前价={current_price:.4f}, 激活价={self.entry_price - self.activation_threshold:.4f}")
        
        # 更新最优价格和追踪线
        if self.is_activated:
            if self.position_direction > 0:  # 多头追踪
                if current_price > self.best_price:
                    self.best_price = current_price
                    # 只增不减：新止损线 = max(旧止损线, 最优价格 - ATR*倍数)
                    new_stop = self.best_price - atr * stop_multiplier
                    self.trailing_stop_price = max(self.trailing_stop_price, new_stop)
                    # 止盈线也向上移动
                    new_profit = self.best_price + atr * profit_multiplier
                    self.trailing_profit_price = max(self.trailing_profit_price, new_profit)
                    
                    self.logger.debug(f"多头追踪更新: 最优价={self.best_price:.4f}, "
                                    f"止损={self.trailing_stop_price:.4f}, 止盈={self.trailing_profit_price:.4f}")
            
            else:  # 空头追踪
                if current_price < self.best_price:
                    self.best_price = current_price
                    # 只增不减：新止损线 = min(旧止损线, 最优价格 + ATR*倍数)
                    new_stop = self.best_price + atr * stop_multiplier
                    self.trailing_stop_price = min(self.trailing_stop_price, new_stop)
                    # 止盈线也向下移动
                    new_profit = self.best_price - atr * profit_multiplier
                    self.trailing_profit_price = min(self.trailing_profit_price, new_profit)
                    
                    self.logger.debug(f"空头追踪更新: 最优价={self.best_price:.4f}, "
                                    f"止损={self.trailing_stop_price:.4f}, 止盈={self.trailing_profit_price:.4f}")
        
        # 检查触发条件
        return self.check_triggers(current_price)
    
    def check_triggers(self, current_price: float):
        """检查止损止盈触发"""
        if not self.is_active or not self.is_activated:
            return None
        
        if self.position_direction > 0:  # 多头
            if current_price <= self.trailing_stop_price:
                self.logger.info(f"多头追踪止损触发: 当前价={current_price:.4f}, 止损线={self.trailing_stop_price:.4f}")
                return "stop_loss"
            elif current_price >= self.trailing_profit_price:
                self.logger.info(f"多头追踪止盈触发: 当前价={current_price:.4f}, 止盈线={self.trailing_profit_price:.4f}")
                return "take_profit"
        
        else:  # 空头
            if current_price >= self.trailing_stop_price:
                self.logger.info(f"空头追踪止损触发: 当前价={current_price:.4f}, 止损线={self.trailing_stop_price:.4f}")
                return "stop_loss"
            elif current_price <= self.trailing_profit_price:
                self.logger.info(f"空头追踪止盈触发: 当前价={current_price:.4f}, 止盈线={self.trailing_profit_price:.4f}")
                return "take_profit"
        
        return None
    
    def get_status(self):
        """获取追踪状态信息"""
        return {
            "is_active": self.is_active,
            "is_activated": self.is_activated,
            "direction": self.position_direction,
            "entry_price": self.entry_price,
            "best_price": self.best_price,
            "stop_price": self.trailing_stop_price,
            "profit_price": self.trailing_profit_price
        }


class VegasStrategy2(BaseStrategy):
    """维加斯隧道交易法策略 - 层次化智能决策版本
    
    核心设计理念：
    1. 大前提内嵌小前提的层次化决策架构
    2. 前置条件下的智能方向筛选
    3. 动态追踪止盈止损系统
    """
    
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()

        # 信号标志
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False

        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 初始化交易价格
        self.long_price = 0
        self.short_price = 0
        
        # 设置日志
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 多时间框架数据管理
        self.confirm_tf_generator = None
        
        # 追踪止盈止损管理器
        self.trailing_manager = TrailingStopProfit(self.logger)

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        trailing_status = self.trailing_manager.get_status()
        return {
            f"Vegas_Fast": self.state_map.vegas_fast,
            f"Vegas_Slow": self.state_map.vegas_slow,
            f"EMA{self.params_map.ema_fast}": self.state_map.ema_fast,
            f"EMA{self.params_map.ema_medium}": self.state_map.ema_medium,
            f"EMA{self.params_map.ema_slow}": self.state_map.ema_slow,
            "BB_Upper": self.state_map.bb_upper,
            "BB_Middle": self.state_map.bb_middle,
            "BB_Lower": self.state_map.bb_lower,
            "Trailing_Stop": trailing_status["stop_price"] if trailing_status["is_active"] else 0,
            "Trailing_Profit": trailing_status["profit_price"] if trailing_status["is_active"] else 0
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "MACD": self.state_map.macd_line,
            "MACD_Signal": self.state_map.macd_signal,
            "MACD_Hist": self.state_map.macd_histogram,
            "RSI": self.state_map.rsi,
            "ADX": self.state_map.adx,
            "Major_Premise": self.state_map.major_premise_score,
            "Minor_Premise": self.state_map.minor_premise_score,
            "Precondition": self.state_map.precondition_score,
            "Final_Direction": self.state_map.final_direction,
            "Price_Energy": self.state_map.price_energy,
            "Market_State": self.state_map.composite_state
        }

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)
        
        # 追踪止盈止损检查
        if self.trading and not self.state_map.pending_order:
            self.check_trailing_stop_profit(tick.last_price)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None
        self.state_map.pending_order = False
        self.logger.info(f"订单已撤销: {order.order_id}")

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交推送回调"""
        super().on_trade(trade, log)
        self.order_id = None
        self.state_map.pending_order = False
        
        action = "开仓" if trade.offset == "open" else "平仓"
        direction = "买入" if trade.direction == "buy" else "卖出"
        
        # 记录详细成交信息
        self.logger.info(f"{action}{direction}: 价格={trade.price}, 数量={trade.volume}, "
                        f"大前提={self.state_map.major_premise_score:.2f}, "
                        f"小前提={self.state_map.minor_premise_score:.2f}, "
                        f"前置条件={self.state_map.precondition_score:.2f}")
        
        # 开仓时初始化追踪止盈止损
        if trade.offset == "open":
            position_direction = 1 if trade.direction == "buy" else -1
            self.trailing_manager.initialize_position(
                direction=position_direction,
                entry_price=trade.price,
                atr=self.state_map.atr,
                activation_ratio=self.params_map.trailing_activation_ratio,
                stop_multiplier=self.params_map.trailing_stop_atr_multiplier,
                profit_multiplier=self.params_map.trailing_profit_atr_multiplier
            )
        
        # 平仓时重置追踪系统
        if trade.offset == "close":
            self.trailing_manager.reset()
            self._check_open_signal_after_close()

    def on_start(self):
        # 主时间框架K线生成器
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        
        # 确认时间框架K线生成器
        self.confirm_tf_generator = KLineGenerator(
            callback=self._confirm_tf_callback,
            real_time_callback=None,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.confirm_timeframe
        )
        
        self.kline_generator.push_history_data()
        self.confirm_tf_generator.push_history_data()

        super().on_start()
        self._reset_state()
        
        self.logger.info(f"层次化智能决策策略启动: {self.params_map.instrument_id}, "
                        f"主周期={self.params_map.kline_style}, "
                        f"确认周期={self.params_map.confirm_timeframe}")
        self.update_status_bar()

    def on_stop(self):
        super().on_stop()
        self.trailing_manager.reset()
        self.logger.info("策略停止")

    def _reset_state(self):
        """重置策略状态"""
        self.signal_price = 0
        self.long_price = 0
        self.short_price = 0

        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

        self.tick = None
        self.state_map.pending_order = False
        self.state_map.kline_count = 0
        
        # 重置历史数据
        self.state_map.high_prices = []
        self.state_map.low_prices = []
        self.state_map.close_prices = []
        self.state_map.open_prices = []
        self.state_map.volume_data = []
        self.state_map.confirm_tf_data = []
        
        # 重置层次化决策状态
        self.state_map.major_premise_score = 0
        self.state_map.minor_premise_score = 0
        self.state_map.precondition_score = 0
        self.state_map.final_direction = 0
        
        # 初始化市场状态
        self.state_map.market_state = {
            'trend': 0.0,
            'volatility': 0.0,
            'momentum': 0.0,
            'composite': 0.0
        }
        
        # 重置追踪系统
        self.trailing_manager.reset()

    def callback(self, kline: KLineData) -> None:
        """主时间框架K线回调"""
        try:
            # 计算所有技术指标
            self.calc_all_indicators(kline)
            
            # 多时间框架隧道确认
            self.calc_tunnel_confirmation()
            
            # 价格隧道位置能量指标
            self.calc_price_energy(kline)
            
            # 市场状态感知分析
            self.market_state_analysis()

            # 如果有挂单，跳过信号处理
            if self.state_map.pending_order:
                self.logger.debug("存在挂单，跳过信号处理")
                self._update_display(kline)
                return

            # 层次化智能决策系统
            self.hierarchical_decision_system(kline)

            # 信号执行
            self.exec_hierarchical_signals()

            # 更新显示
            self._update_display(kline)

        except Exception as e:
            self.logger.error(f"K线回调异常: {e}")

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        try:
            self.calc_all_indicators(kline, is_realtime=True)
            self.calc_price_energy(kline, is_realtime=True)
            self._update_display(kline)
        except Exception as e:
            self.logger.error(f"实时回调异常: {e}")

    def _confirm_tf_callback(self, kline: KLineData) -> None:
        """确认时间框架K线回调"""
        try:
            # 存储确认时间框架数据
            self.state_map.confirm_tf_data.append({
                'close': kline.close,
                'high': kline.high,
                'low': kline.low,
                'timestamp': getattr(kline, 'timestamp', datetime.now())
            })
            
            # 保持数据长度
            max_confirm_history = max(self.params_map.vegas_fast, self.params_map.vegas_slow) + 20
            if len(self.state_map.confirm_tf_data) > max_confirm_history:
                self.state_map.confirm_tf_data = self.state_map.confirm_tf_data[-max_confirm_history:]
                
        except Exception as e:
            self.logger.error(f"确认时间框架回调异常: {e}")

    def check_trailing_stop_profit(self, current_price: float):
        """检查追踪止盈止损触发"""
        try:
            if not self.trailing_manager.is_active:
                return
            
            # 更新追踪系统
            trigger_result = self.trailing_manager.update(
                current_price=current_price,
                atr=self.state_map.atr,
                stop_multiplier=self.params_map.trailing_stop_atr_multiplier,
                profit_multiplier=self.params_map.trailing_profit_atr_multiplier
            )
            
            # 处理触发结果
            if trigger_result:
                self._execute_trailing_close(trigger_result, current_price)
                
        except Exception as e:
            self.logger.error(f"追踪止盈止损检查异常: {e}")

    def _execute_trailing_close(self, trigger_type: str, current_price: float):
        """执行追踪平仓"""
        if self.state_map.pending_order:
            return
        
        try:
            position = self.get_position(self.params_map.instrument_id)
            
            if position.net_position == 0:
                return
            
            # 获取交易价格
            self.long_price, self.short_price = self.get_trade_price()
            
            reason = "追踪止损" if trigger_type == "stop_loss" else "追踪止盈"
            
            if position.net_position > 0:  # 平多仓
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                self.logger.info(f"{reason}平多仓: 价格={current_price:.4f}")
                
            elif position.net_position < 0:  # 平空仓
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                self.logger.info(f"{reason}平空仓: 价格={current_price:.4f}")
                
        except Exception as e:
            self.logger.error(f"追踪平仓执行异常: {e}")

    def hierarchical_decision_system(self, kline: KLineData):
        """层次化智能决策系统
        
        架构：大前提内嵌小前提，在前置条件下智能筛选多空方向
        
        大前提：市场环境和趋势确认（权重最高）
        小前提：技术指标组合确认（权重中等）
        前置条件：风险控制和过滤条件（权重最低但必须满足）
        """
        
        self._reset_signals()
        
        try:
            position = self.get_position(self.params_map.instrument_id)
            current_position = position.net_position
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            return
        
        if self.state_map.kline_count < max(self.params_map.vegas_slow, self.params_map.bb_period):
            return
        
        # 第一层：大前提 - 市场环境和趋势确认
        self.state_map.major_premise_score = self._evaluate_major_premise()
        
        # 第二层：小前提 - 技术指标组合确认
        self.state_map.minor_premise_score = self._evaluate_minor_premise()
        
        # 第三层：前置条件 - 风险控制和过滤
        self.state_map.precondition_score = self._evaluate_preconditions()
        
        # 层次化决策逻辑
        self._make_hierarchical_decision(current_position)
        
        # 更新交易价格
        self._update_trade_prices(kline)
        
        # 记录决策过程
        self._log_decision_process()

    def _evaluate_major_premise(self) -> float:
        """评估大前提：市场环境和趋势确认
        
        包含：
        1. 多时间框架隧道强度确认 (40%)
        2. 市场状态综合评估 (35%)
        3. ADX趋势强度确认 (25%)
        """
        try:
            scores = []
            weights = []
            
            # 1. 多时间框架隧道强度 (40%)
            tunnel_score = self.state_map.tunnel_strength  # -1到1
            scores.append(tunnel_score)
            weights.append(0.40)
            
            # 2. 市场状态综合评估 (35%)
            market_score = self.state_map.composite_state  # -1到1
            scores.append(market_score)
            weights.append(0.35)
            
            # 3. ADX趋势强度确认 (25%)
            if self.state_map.adx >= self.params_map.adx_threshold:
                # ADX强度足够，根据DI方向给分
                if self.state_map.di_plus > self.state_map.di_minus:
                    adx_score = min(1.0, (self.state_map.adx - self.params_map.adx_threshold) / 25)
                else:
                    adx_score = -min(1.0, (self.state_map.adx - self.params_map.adx_threshold) / 25)
            else:
                adx_score = 0  # 趋势不够强
            scores.append(adx_score)
            weights.append(0.25)
            
            # 加权平均
            major_premise = sum(s * w for s, w in zip(scores, weights))
            
            self.logger.debug(f"大前提评估: 隧道={tunnel_score:.3f}, 市场={market_score:.3f}, "
                            f"ADX={adx_score:.3f}, 综合={major_premise:.3f}")
            
            return major_premise
            
        except Exception as e:
            self.logger.error(f"大前提评估异常: {e}")
            return 0.0

    def _evaluate_minor_premise(self) -> float:
        """评估小前提：技术指标组合确认
        
        包含：
        1. EMA多重趋势确认 (30%)
        2. MACD动量确认 (25%)
        3. 价格隧道位置能量 (25%)
        4. 布林带位置确认 (20%)
        """
        try:
            scores = []
            weights = []
            
            # 1. EMA多重趋势确认 (30%)
            ema_score = self._calc_ema_trend_score()
            scores.append(ema_score)
            weights.append(0.30)
            
            # 2. MACD动量确认 (25%)
            macd_score = self._calc_macd_momentum_score()
            scores.append(macd_score)
            weights.append(0.25)
            
            # 3. 价格隧道位置能量 (25%)
            energy_score = self._normalize_energy_score()
            scores.append(energy_score)
            weights.append(0.25)
            
            # 4. 布林带位置确认 (20%)
            bb_score = self._calc_bb_position_score()
            scores.append(bb_score)
            weights.append(0.20)
            
            # 加权平均
            minor_premise = sum(s * w for s, w in zip(scores, weights))
            
            self.logger.debug(f"小前提评估: EMA={ema_score:.3f}, MACD={macd_score:.3f}, "
                            f"能量={energy_score:.3f}, BB={bb_score:.3f}, 综合={minor_premise:.3f}")
            
            return minor_premise
            
        except Exception as e:
            self.logger.error(f"小前提评估异常: {e}")
            return 0.0

    def _evaluate_preconditions(self) -> float:
        """评估前置条件：风险控制和过滤
        
        包含：
        1. RSI过热过冷过滤 (40%)
        2. 成交量确认 (35%)
        3. 波动率适宜性 (25%)
        """
        try:
            scores = []
            weights = []
            
            # 1. RSI过热过冷过滤 (40%)
            rsi_score = self._calc_rsi_filter_score()
            scores.append(rsi_score)
            weights.append(0.40)
            
            # 2. 成交量确认 (35%)
            volume_score = self._calc_volume_confirmation_score()
            scores.append(volume_score)
            weights.append(0.35)
            
            # 3. 波动率适宜性 (25%)
            volatility_score = self._calc_volatility_suitability_score()
            scores.append(volatility_score)
            weights.append(0.25)
            
            # 加权平均
            precondition = sum(s * w for s, w in zip(scores, weights))
            
            self.logger.debug(f"前置条件评估: RSI={rsi_score:.3f}, 成交量={volume_score:.3f}, "
                            f"波动率={volatility_score:.3f}, 综合={precondition:.3f}")
            
            return precondition
            
        except Exception as e:
            self.logger.error(f"前置条件评估异常: {e}")
            return 0.0

    def _make_hierarchical_decision(self, current_position: int):
        """层次化决策逻辑
        
        决策规则：
        1. 大前提必须超过阈值（最重要）
        2. 小前提必须超过阈值（重要）
        3. 前置条件必须超过阈值（必要）
        4. 三者方向一致时才产生信号
        """
        
        # 重置最终方向
        self.state_map.final_direction = 0
        
        # 检查各层次阈值
        major_pass = abs(self.state_map.major_premise_score) >= self.params_map.major_premise_threshold
        minor_pass = abs(self.state_map.minor_premise_score) >= self.params_map.minor_premise_threshold
        precond_pass = abs(self.state_map.precondition_score) >= self.params_map.precondition_threshold
        
        if not (major_pass and minor_pass and precond_pass):
            self.logger.debug(f"层次化决策未通过: 大前提={major_pass}, 小前提={minor_pass}, 前置条件={precond_pass}")
            return
        
        # 检查方向一致性
        major_direction = 1 if self.state_map.major_premise_score > 0 else -1
        minor_direction = 1 if self.state_map.minor_premise_score > 0 else -1
        precond_direction = 1 if self.state_map.precondition_score > 0 else -1
        
        # 三者方向必须一致
        if major_direction == minor_direction == precond_direction:
            self.state_map.final_direction = major_direction
            
            # 根据持仓状态和最终方向生成信号
            if current_position == 0:  # 空仓
                if self.state_map.final_direction > 0:
                    self.buy_signal = True
                    self.logger.info(f"层次化决策：开多仓信号 - 大前提={self.state_map.major_premise_score:.3f}, "
                                   f"小前提={self.state_map.minor_premise_score:.3f}, "
                                   f"前置条件={self.state_map.precondition_score:.3f}")
                elif self.state_map.final_direction < 0:
                    self.short_signal = True
                    self.logger.info(f"层次化决策：开空仓信号 - 大前提={self.state_map.major_premise_score:.3f}, "
                                   f"小前提={self.state_map.minor_premise_score:.3f}, "
                                   f"前置条件={self.state_map.precondition_score:.3f}")
            
            elif current_position > 0:  # 多仓
                if self.state_map.final_direction < 0:
                    self.sell_signal = True
                    self.logger.info(f"层次化决策：平多仓信号")
            
            elif current_position < 0:  # 空仓
                if self.state_map.final_direction > 0:
                    self.cover_signal = True
                    self.logger.info(f"层次化决策：平空仓信号")
        
        else:
            self.logger.debug(f"方向不一致: 大前提={major_direction}, 小前提={minor_direction}, 前置条件={precond_direction}")

    def _log_decision_process(self):
        """记录决策过程"""
        if abs(self.state_map.major_premise_score) >= 0.3:  # 只记录有意义的决策
            direction_text = "多头" if self.state_map.final_direction > 0 else "空头" if self.state_map.final_direction < 0 else "中性"
            
            self.logger.info(f"层次化决策结果: {direction_text} - "
                           f"大前提={self.state_map.major_premise_score:.3f}({self.params_map.major_premise_threshold}), "
                           f"小前提={self.state_map.minor_premise_score:.3f}({self.params_map.minor_premise_threshold}), "
                           f"前置条件={self.state_map.precondition_score:.3f}({self.params_map.precondition_threshold})")

    # 辅助评分方法
    def _calc_ema_trend_score(self) -> float:
        """计算EMA趋势得分"""
        try:
            scores = []
            
            # 快中慢EMA排列
            if self.state_map.ema_fast > self.state_map.ema_medium > self.state_map.ema_slow:
                scores.append(1.0)  # 完美多头排列
            elif self.state_map.ema_fast < self.state_map.ema_medium < self.state_map.ema_slow:
                scores.append(-1.0)  # 完美空头排列
            else:
                # 部分排列，根据快慢线关系给分
                if self.state_map.ema_fast > self.state_map.ema_slow:
                    scores.append(0.5)
                elif self.state_map.ema_fast < self.state_map.ema_slow:
                    scores.append(-0.5)
                else:
                    scores.append(0.0)
            
            # 维加斯隧道排列
            if self.state_map.vegas_fast > self.state_map.vegas_slow:
                scores.append(1.0)
            elif self.state_map.vegas_fast < self.state_map.vegas_slow:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            return np.mean(scores)
            
        except Exception as e:
            self.logger.error(f"EMA趋势得分计算异常: {e}")
            return 0.0

    def _calc_macd_momentum_score(self) -> float:
        """计算MACD动量得分"""
        try:
            scores = []
            
            # MACD线位置
            if self.state_map.macd_line > 0:
                scores.append(1.0)
            elif self.state_map.macd_line < 0:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            # MACD与信号线关系
            if self.state_map.macd_line > self.state_map.macd_signal:
                scores.append(1.0)
            elif self.state_map.macd_line < self.state_map.macd_signal:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            # MACD柱状图
            if self.state_map.macd_histogram > 0:
                scores.append(1.0)
            elif self.state_map.macd_histogram < 0:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            return np.mean(scores)
            
        except Exception as e:
            self.logger.error(f"MACD动量得分计算异常: {e}")
            return 0.0

    def _normalize_energy_score(self) -> float:
        """标准化能量得分"""
        try:
            # 将能量值标准化到-1到1范围
            if abs(self.state_map.price_energy) < 1e-6:
                return 0.0
            
            # 使用tanh函数进行标准化，能量值除以50作为缩放因子
            normalized = math.tanh(self.state_map.price_energy / 50)
            return normalized
            
        except Exception as e:
            self.logger.error(f"能量得分标准化异常: {e}")
            return 0.0

    def _calc_bb_position_score(self) -> float:
        """计算布林带位置得分"""
        try:
            if len(self.state_map.close_prices) == 0:
                return 0.0
            
            current_price = self.state_map.close_prices[-1]
            bb_width = self.state_map.bb_upper - self.state_map.bb_lower
            
            if bb_width < 1e-6:
                return 0.0
            
            # 计算价格在布林带中的相对位置
            position = (current_price - self.state_map.bb_lower) / bb_width
            
            # 转换为-1到1的得分
            # 0.8以上为强多头区域，0.2以下为强空头区域
            if position > 0.8:
                return min(1.0, (position - 0.8) / 0.2 + 0.5)
            elif position < 0.2:
                return max(-1.0, (position - 0.2) / 0.2 - 0.5)
            else:
                # 中性区域，根据相对中线位置给分
                return (position - 0.5) * 2
            
        except Exception as e:
            self.logger.error(f"布林带位置得分计算异常: {e}")
            return 0.0

    def _calc_rsi_filter_score(self) -> float:
        """计算RSI过滤得分"""
        try:
            # RSI过热过冷过滤
            if self.state_map.rsi > self.params_map.rsi_overbought:
                # 超买区域，偏向空头
                return -min(1.0, (self.state_map.rsi - self.params_map.rsi_overbought) / (100 - self.params_map.rsi_overbought))
            elif self.state_map.rsi < self.params_map.rsi_oversold:
                # 超卖区域，偏向多头
                return min(1.0, (self.params_map.rsi_oversold - self.state_map.rsi) / self.params_map.rsi_oversold)
            else:
                # 正常区域，根据相对50的位置给分
                return (self.state_map.rsi - 50) / 50 * 0.5  # 降低权重
            
        except Exception as e:
            self.logger.error(f"RSI过滤得分计算异常: {e}")
            return 0.0

    def _calc_volume_confirmation_score(self) -> float:
        """计算成交量确认得分"""
        try:
            if self.state_map.volume_ratio < 1e-6:
                return 0.0
            
            # 成交量放大确认
            if self.state_map.volume_ratio >= self.params_map.volume_threshold:
                # 成交量放大，给正分
                volume_score = min(1.0, (self.state_map.volume_ratio - 1.0) / 2.0)
            else:
                # 成交量萎缩，给负分
                volume_score = -min(1.0, (1.0 - self.state_map.volume_ratio) / 0.5)
            
            return volume_score
            
        except Exception as e:
            self.logger.error(f"成交量确认得分计算异常: {e}")
            return 0.0

    def _calc_volatility_suitability_score(self) -> float:
        """计算波动率适宜性得分"""
        try:
            if self.state_map.atr == 0 or len(self.state_map.close_prices) == 0:
                return 0.0
            
            current_price = self.state_map.close_prices[-1]
            volatility_ratio = self.state_map.atr / current_price
            
            # 适宜的波动率范围：0.01-0.05
            if 0.01 <= volatility_ratio <= 0.05:
                return 1.0  # 理想波动率
            elif volatility_ratio < 0.01:
                return max(0.0, volatility_ratio / 0.01)  # 波动率过低
            else:
                return max(0.0, 1.0 - (volatility_ratio - 0.05) / 0.05)  # 波动率过高
            
        except Exception as e:
            self.logger.error(f"波动率适宜性得分计算异常: {e}")
            return 0.0

    def exec_hierarchical_signals(self):
        """执行层次化信号"""
        if self.state_map.pending_order:
            return

        try:
            position = self.get_position(self.params_map.instrument_id)
            
            # 开多仓
            if self.buy_signal and position.net_position == 0:
                self.order_id = self.auto_open_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=self.params_map.order_volume,
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                self.signal_price = self.long_price
                
            # 开空仓
            elif self.short_signal and position.net_position == 0:
                self.order_id = self.auto_open_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=self.params_map.order_volume,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                self.signal_price = self.short_price
                
            # 平多仓
            elif self.sell_signal and position.net_position > 0:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                self.signal_price = self.short_price
                
            # 平空仓
            elif self.cover_signal and position.net_position < 0:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                self.signal_price = self.long_price

        except Exception as e:
            self.logger.error(f"信号执行异常: {e}")

    # 以下是技术指标计算方法（与之前版本相同）
    def calc_all_indicators(self, kline: KLineData, is_realtime: bool = False) -> None:
        """计算所有技术指标"""
        close_price = kline.close
        high_price = kline.high
        low_price = kline.low
        open_price = getattr(kline, 'open', close_price)
        volume = getattr(kline, 'volume', 0)
        
        if not is_realtime:
            self.state_map.kline_count += 1
            
            # 更新历史数据
            self.state_map.high_prices.append(high_price)
            self.state_map.low_prices.append(low_price)
            self.state_map.close_prices.append(close_price)
            self.state_map.open_prices.append(open_price)
            self.state_map.volume_data.append(volume)
            
            # 保持历史长度
            max_history = max(self.params_map.vegas_slow, self.params_map.bb_period, 
                            self.params_map.adx_period, self.params_map.avg_volume_period) + 20
            if len(self.state_map.close_prices) > max_history:
                self.state_map.high_prices = self.state_map.high_prices[-max_history:]
                self.state_map.low_prices = self.state_map.low_prices[-max_history:]
                self.state_map.close_prices = self.state_map.close_prices[-max_history:]
                self.state_map.open_prices = self.state_map.open_prices[-max_history:]
                self.state_map.volume_data = self.state_map.volume_data[-max_history:]
        
        if not is_realtime and len(self.state_map.close_prices) >= 2:
            # 计算维加斯隧道
            self._calc_vegas_tunnel()
            
            # 计算多重EMA系统
            self._calc_ema_system()
            
            # 计算其他技术指标
            self._calc_macd()
            self._calc_rsi()
            self._calc_bollinger_bands()
            self._calc_volume_indicators()
            self._calc_adx()
            self._calc_atr()

    def _calc_vegas_tunnel(self):
        """计算维加斯隧道核心指标"""
        closes = np.array(self.state_map.close_prices)
        
        # 计算维加斯隧道EMA
        self.state_map.vegas_fast = self._ema(closes, self.params_map.vegas_fast)
        self.state_map.vegas_slow = self._ema(closes, self.params_map.vegas_slow)

    def _calc_ema_system(self):
        """计算多重EMA系统"""
        closes = np.array(self.state_map.close_prices)
        
        self.state_map.ema_fast = self._ema(closes, self.params_map.ema_fast)
        self.state_map.ema_medium = self._ema(closes, self.params_map.ema_medium)
        self.state_map.ema_slow = self._ema(closes, self.params_map.ema_slow)

    def _calc_macd(self):
        """计算MACD指标"""
        closes = np.array(self.state_map.close_prices)
        
        ema_fast = self._ema(closes, self.params_map.macd_fast)
        ema_slow = self._ema(closes, self.params_map.macd_slow)
        
        self.state_map.macd_line = ema_fast - ema_slow
        
        # 计算MACD信号线（MACD的EMA）
        if hasattr(self, '_macd_history'):
            self._macd_history.append(self.state_map.macd_line)
        else:
            self._macd_history = [self.state_map.macd_line]
        
        if len(self._macd_history) > 100:
            self._macd_history = self._macd_history[-100:]
        
        if len(self._macd_history) >= self.params_map.macd_signal:
            macd_array = np.array(self._macd_history)
            self.state_map.macd_signal = self._ema(macd_array, self.params_map.macd_signal)
        else:
            self.state_map.macd_signal = self.state_map.macd_line
        
        self.state_map.macd_histogram = self.state_map.macd_line - self.state_map.macd_signal

    def _calc_rsi(self):
        """计算RSI指标"""
        if len(self.state_map.close_prices) < self.params_map.rsi_period + 1:
            self.state_map.rsi = 50
            return
        
        closes = np.array(self.state_map.close_prices)
        deltas = np.diff(closes)
        
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-self.params_map.rsi_period:])
        avg_loss = np.mean(losses[-self.params_map.rsi_period:])
        
        if avg_loss == 0:
            self.state_map.rsi = 100
        else:
            rs = avg_gain / avg_loss
            self.state_map.rsi = 100 - (100 / (1 + rs))

    def _calc_bollinger_bands(self):
        """计算布林带指标"""
        if len(self.state_map.close_prices) < self.params_map.bb_period:
            return
        
        closes = np.array(self.state_map.close_prices[-self.params_map.bb_period:])
        
        self.state_map.bb_middle = np.mean(closes)
        std = np.std(closes)
        
        self.state_map.bb_upper = self.state_map.bb_middle + (std * self.params_map.bb_std)
        self.state_map.bb_lower = self.state_map.bb_middle - (std * self.params_map.bb_std)
        
        # 计算布林带百分比
        current_price = self.state_map.close_prices[-1]
        if self.state_map.bb_upper != self.state_map.bb_lower:
            self.state_map.bb_percent = (current_price - self.state_map.bb_lower) / (self.state_map.bb_upper - self.state_map.bb_lower)
        else:
            self.state_map.bb_percent = 0.5

    def _calc_volume_indicators(self):
        """计算成交量指标"""
        if len(self.state_map.volume_data) < self.params_map.volume_ma_period:
            return
        
        volumes = np.array(self.state_map.volume_data)
        
        # 成交量均线
        self.state_map.volume_ma = np.mean(volumes[-self.params_map.volume_ma_period:])
        
        # 成交量比率
        current_volume = volumes[-1]
        if self.state_map.volume_ma > 0:
            self.state_map.volume_ratio = current_volume / self.state_map.volume_ma
        else:
            self.state_map.volume_ratio = 1.0
        
        # 平均成交量
        if len(volumes) >= self.params_map.avg_volume_period:
            self.state_map.avg_volume = np.mean(volumes[-self.params_map.avg_volume_period:])
        else:
            self.state_map.avg_volume = self.state_map.volume_ma

    def _calc_adx(self):
        """计算ADX指标"""
        if len(self.state_map.high_prices) < self.params_map.adx_period + 1:
            return
        
        highs = np.array(self.state_map.high_prices)
        lows = np.array(self.state_map.low_prices)
        closes = np.array(self.state_map.close_prices)
        
        # 计算TR (True Range)
        tr1 = highs[1:] - lows[1:]
        tr2 = np.abs(highs[1:] - closes[:-1])
        tr3 = np.abs(lows[1:] - closes[:-1])
        tr = np.maximum(tr1, np.maximum(tr2, tr3))
        
        # 计算DM (Directional Movement)
        dm_plus = np.where((highs[1:] - highs[:-1]) > (lows[:-1] - lows[1:]), 
                          np.maximum(highs[1:] - highs[:-1], 0), 0)
        dm_minus = np.where((lows[:-1] - lows[1:]) > (highs[1:] - highs[:-1]), 
                           np.maximum(lows[:-1] - lows[1:], 0), 0)
        
        # 计算平滑的TR和DM
        if len(tr) >= self.params_map.adx_period:
            atr = np.mean(tr[-self.params_map.adx_period:])
            adm_plus = np.mean(dm_plus[-self.params_map.adx_period:])
            adm_minus = np.mean(dm_minus[-self.params_map.adx_period:])
            
            # 计算DI
            if atr > 0:
                self.state_map.di_plus = (adm_plus / atr) * 100
                self.state_map.di_minus = (adm_minus / atr) * 100
                
                # 计算ADX
                di_sum = self.state_map.di_plus + self.state_map.di_minus
                if di_sum > 0:
                    dx = abs(self.state_map.di_plus - self.state_map.di_minus) / di_sum * 100
                    self.state_map.adx = dx
                else:
                    self.state_map.adx = 0
            else:
                self.state_map.di_plus = 0
                self.state_map.di_minus = 0
                self.state_map.adx = 0

    def _calc_atr(self):
        """计算ATR指标"""
        if len(self.state_map.high_prices) < self.params_map.atr_period + 1:
            return
        
        highs = np.array(self.state_map.high_prices)
        lows = np.array(self.state_map.low_prices)
        closes = np.array(self.state_map.close_prices)
        
        # 计算True Range
        tr1 = highs[1:] - lows[1:]
        tr2 = np.abs(highs[1:] - closes[:-1])
        tr3 = np.abs(lows[1:] - closes[:-1])
        tr = np.maximum(tr1, np.maximum(tr2, tr3))
        
        if len(tr) >= self.params_map.atr_period:
            self.state_map.atr = np.mean(tr[-self.params_map.atr_period:])

    def calc_tunnel_confirmation(self):
        """多时间框架隧道确认系统"""
        if len(self.state_map.confirm_tf_data) < max(self.params_map.vegas_fast, self.params_map.vegas_slow):
            self.state_map.tunnel_strength = 0
            self.state_map.tunnel_width = 0
            return
        
        try:
            # 提取确认时间框架的收盘价
            confirm_closes = np.array([data['close'] for data in self.state_map.confirm_tf_data])
            
            # 计算确认时间框架的维加斯隧道
            confirm_fast = self._ema(confirm_closes, self.params_map.vegas_fast)
            confirm_slow = self._ema(confirm_closes, self.params_map.vegas_slow)
            
            current_price = confirm_closes[-1]
            
            # 计算隧道宽度百分比
            self.state_map.tunnel_width = abs(confirm_fast - confirm_slow) / current_price
            
            # 判断隧道强度
            if self.state_map.tunnel_width > self.params_map.tunnel_width_threshold:
                if confirm_fast > confirm_slow:
                    self.state_map.tunnel_strength = 1.0  # 强多头
                else:
                    self.state_map.tunnel_strength = -1.0  # 强空头
                    
                self.logger.debug(f"隧道确认: 强度={self.state_map.tunnel_strength}, "
                                f"宽度={self.state_map.tunnel_width:.4f}")
            else:
                self.state_map.tunnel_strength = 0  # 隧道太窄，无明确方向
                
        except Exception as e:
            self.logger.error(f"隧道确认计算异常: {e}")
            self.state_map.tunnel_strength = 0
            self.state_map.tunnel_width = 0

    def calc_price_energy(self, kline: KLineData, is_realtime: bool = False):
        """价格隧道位置能量指标"""
        if len(self.state_map.close_prices) < 2:
            self.state_map.price_energy = 0
            return
        
        try:
            close_price = kline.close
            open_price = getattr(kline, 'open', close_price)
            volume = getattr(kline, 'volume', 0)
            
            # 计算平均成交量
            if not is_realtime and len(self.state_map.volume_data) >= self.params_map.avg_volume_period:
                self.state_map.avg_volume = np.mean(self.state_map.volume_data[-self.params_map.avg_volume_period:])
            
            if self.state_map.avg_volume == 0:
                self.state_map.price_energy = 0
                return
            
            # 计算位置 = (收盘价-慢线)/(快线-慢线)
            if abs(self.state_map.vegas_fast - self.state_map.vegas_slow) < 1e-6:
                position = 0
            else:
                position = (close_price - self.state_map.vegas_slow) / (self.state_map.vegas_fast - self.state_map.vegas_slow)
            
            # 计算动量 = 收盘价/开盘价 - 1
            if open_price > 0:
                momentum = close_price / open_price - 1
            else:
                momentum = 0
            
            # 计算成交量因子 = log(当前成交量/平均成交量 + 1)
            volume_factor = math.log(volume / self.state_map.avg_volume + 1) if self.state_map.avg_volume > 0 else 0
            
            # 计算能量 = 位置 × 动量 × 成交量因子
            self.state_map.price_energy = position * momentum * volume_factor * 100  # 放大100倍便于显示
            
            if not is_realtime:
                self.logger.debug(f"价格能量: {self.state_map.price_energy:.2f}, "
                                f"位置={position:.3f}, 动量={momentum:.4f}, 成交量因子={volume_factor:.3f}")
                
        except Exception as e:
            self.logger.error(f"价格能量计算异常: {e}")
            self.state_map.price_energy = 0

    def market_state_analysis(self):
        """市场状态感知引擎"""
        try:
            # 1. 趋势得分：3个时间框架趋势方向一致性
            trend_score = self._calc_trend_score()
            
            # 2. 波动率得分：ATR/收盘价 标准化
            volatility_score = self._calc_volatility_score()
            
            # 3. 动量得分：RSI位置标准化
            momentum_score = self._calc_momentum_score()
            
            # 更新状态
            self.state_map.trend_score = trend_score
            self.state_map.volatility_score = volatility_score
            self.state_map.momentum_score = momentum_score
            
            # 综合状态 = 0.5×趋势 + 0.3×波动 + 0.2×动量
            self.state_map.composite_state = (
                trend_score * 0.5 +
                volatility_score * 0.3 +
                momentum_score * 0.2
            )
            
            # 更新市场状态字典
            self.state_map.market_state = {
                'trend': trend_score,
                'volatility': volatility_score,
                'momentum': momentum_score,
                'composite': self.state_map.composite_state
            }
            
            self.logger.debug(f"市场状态: 趋势={trend_score:.3f}, 波动={volatility_score:.3f}, "
                            f"动量={momentum_score:.3f}, 综合={self.state_map.composite_state:.3f}")
            
        except Exception as e:
            self.logger.error(f"市场状态分析异常: {e}")

    def _calc_trend_score(self) -> float:
        """计算趋势得分"""
        try:
            scores = []
            
            # 主时间框架趋势
            if self.state_map.ema_fast > self.state_map.ema_slow:
                scores.append(1.0)
            elif self.state_map.ema_fast < self.state_map.ema_slow:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            # 维加斯隧道趋势
            if self.state_map.vegas_fast > self.state_map.vegas_slow:
                scores.append(1.0)
            elif self.state_map.vegas_fast < self.state_map.vegas_slow:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            # 确认时间框架趋势
            scores.append(self.state_map.tunnel_strength)
            
            # 计算一致性得分
            return np.mean(scores)
            
        except Exception as e:
            self.logger.error(f"趋势得分计算异常: {e}")
            return 0.0

    def _calc_volatility_score(self) -> float:
        """计算波动率得分"""
        try:
            if self.state_map.atr == 0 or len(self.state_map.close_prices) == 0:
                return 0.0
            
            current_price = self.state_map.close_prices[-1]
            volatility_ratio = self.state_map.atr / current_price
            
            # 标准化到-1到1范围（假设正常波动率在0.01-0.05之间）
            normalized = (volatility_ratio - 0.03) / 0.02
            return max(-1.0, min(1.0, normalized))
            
        except Exception as e:
            self.logger.error(f"波动率得分计算异常: {e}")
            return 0.0

    def _calc_momentum_score(self) -> float:
        """计算动量得分"""
        try:
            # RSI位置标准化到-1到1
            return (self.state_map.rsi - 50) / 50
            
        except Exception as e:
            self.logger.error(f"动量得分计算异常: {e}")
            return 0.0

    def _ema(self, data: np.ndarray, period: int) -> float:
        """计算指数移动平均"""
        if len(data) < period:
            return np.mean(data)
        
        alpha = 2.0 / (period + 1)
        ema = data[0]
        
        for price in data[1:]:
            ema = alpha * price + (1 - alpha) * ema
        
        return ema

    def _reset_signals(self):
        """重置信号标志"""
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

    def _update_trade_prices(self, kline: KLineData):
        """更新交易价格"""
        try:
            self.long_price, self.short_price = self.get_trade_price()
        except Exception as e:
            self.logger.error(f"更新交易价格异常: {e}")
            self.long_price = kline.close
            self.short_price = kline.close

    def _update_display(self, kline: KLineData):
        """更新显示信息"""
        try:
            self.update_status_bar()
        except Exception as e:
            self.logger.error(f"更新显示异常: {e}")

    def _check_open_signal_after_close(self):
        """平仓后检查开仓信号"""
        # 平仓后立即检查是否有反向开仓信号
        pass

    def update_status_bar(self):
        """更新状态栏"""
        try:
            trailing_status = self.trailing_manager.get_status()
            status_text = (f"层次化决策 | "
                          f"大前提:{self.state_map.major_premise_score:.2f} | "
                          f"小前提:{self.state_map.minor_premise_score:.2f} | "
                          f"前置条件:{self.state_map.precondition_score:.2f} | "
                          f"方向:{self.state_map.final_direction} | "
                          f"追踪:{'激活' if trailing_status['is_activated'] else '待机'}")
            
            # 这里应该调用实际的状态栏更新方法
            # self.set_status_bar(status_text)
            
        except Exception as e:
            self.logger.error(f"状态栏更新异常: {e}")