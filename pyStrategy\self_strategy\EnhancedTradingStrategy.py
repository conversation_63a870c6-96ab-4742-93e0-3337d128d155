from typing import Literal, List, Optional

import numpy as np

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """增强交易策略参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M5", title="K 线周期")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 新增增强参数
    max_position: int = Field(default=1, title="最大持仓数")
    stop_loss_multiplier: float = Field(default=2.0, title="止损倍数")
    trail_profit_ratio: float = Field(default=0.5, title="追踪止盈比例")
    trade_direction: Literal["buy", "sell", "auto"] = Field(default="auto", title="交易方向")
    
    # 技术指标参数
    ema_fast: int = Field(default=12, title="快速EMA周期")
    ema_slow: int = Field(default=26, title="慢速EMA周期")
    rsi_period: int = Field(default=14, title="RSI周期")
    atr_period: int = Field(default=14, title="ATR周期")


class State(BaseState):
    """增强交易策略状态映射模型"""
    # 技术指标
    ema_fast: float = Field(default=0.0, title="快速EMA")
    ema_slow: float = Field(default=0.0, title="慢速EMA")
    rsi: float = Field(default=50.0, title="RSI")
    atr: float = Field(default=0.0, title="真实波幅")
    
    # 仓位和风险管理
    stop_price: float = Field(default=0.0, title="止损价格")
    position_direction: Optional[Literal["long", "short"]] = Field(default=None, title="持仓方向")
    current_trend: Literal["up", "down", "neutral"] = Field(default="neutral", title="当前趋势")
    
    # 反转检测
    prev_kline: List[dict] = Field(default_factory=list, title="前3根K线")
    reversal_detected: bool = Field(default=False, title="反转检测")


class EnhancedTradingStrategy(BaseStrategy):
    """增强交易策略 - 支持自动趋势跟踪和动态止损"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        """参数表"""

        self.state_map = State()
        """状态表"""

        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False

        self.tick: TickData = None

        # 前一周期指标值用于趋势变化检测
        self.prev_ema_fast = 0.0
        self.prev_ema_slow = 0.0
        self.prev_rsi = 50.0
        self.prev_trend = "neutral"

        self.order_id = None
        self.signal_price = 0
        self.entry_price = 0.0  # 开仓价格
        self.max_favorable_price = 0.0  # 最有利价格（用于追踪止损）

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        data = {
            f"EMA{self.params_map.ema_fast}": self.state_map.ema_fast,
            f"EMA{self.params_map.ema_slow}": self.state_map.ema_slow,
        }
        
        # 只有在有止损价格时才显示
        if self.state_map.stop_price > 0:
            data["STOP"] = self.state_map.stop_price
            
        return data

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "RSI": self.state_map.rsi,
            "ATR": self.state_map.atr,
            "TREND": 1 if self.state_map.current_trend == "up" else (-1 if self.state_map.current_trend == "down" else 0)
        }
    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交推送回调"""
        super().on_trade(trade, log)
        self.order_id = None
        
        # 更新持仓方向和开仓价格
        position = self.get_position(self.params_map.instrument_id)
        if position.net_position > 0:
            self.state_map.position_direction = "long"
            if self.entry_price == 0.0:
                self.entry_price = trade.price
                self.max_favorable_price = trade.price
        elif position.net_position < 0:
            self.state_map.position_direction = "short"
            if self.entry_price == 0.0:
                self.entry_price = trade.price
                self.max_favorable_price = trade.price
        else:
            self.state_map.position_direction = None
            self.entry_price = 0.0
            self.max_favorable_price = 0.0
            self.state_map.stop_price = 0.0

    def validate_parameters(self) -> bool:
        """验证策略参数"""
        try:
            # 检查基本参数
            if not self.params_map.exchange or not self.params_map.instrument_id:
                print("错误：交易所代码和合约代码不能为空")
                return False
                
            # 检查数值参数范围
            if self.params_map.max_position <= 0:
                print("错误：最大持仓数必须大于0")
                return False
                
            if self.params_map.stop_loss_multiplier <= 0:
                print("错误：止损倍数必须大于0")
                return False
                
            if not (0 < self.params_map.trail_profit_ratio <= 1):
                print("错误：追踪止盈比例必须在0-1之间")
                return False
                
            if self.params_map.order_volume <= 0:
                print("错误：报单数量必须大于0")
                return False
                
            # 检查技术指标参数
            if self.params_map.ema_fast <= 0 or self.params_map.ema_slow <= 0:
                print("错误：EMA周期必须大于0")
                return False
                
            if self.params_map.ema_fast >= self.params_map.ema_slow:
                print("错误：快速EMA周期必须小于慢速EMA周期")
                return False
                
            if self.params_map.rsi_period <= 0 or self.params_map.atr_period <= 0:
                print("错误：RSI和ATR周期必须大于0")
                return False
                
            return True
            
        except Exception as e:
            print(f"参数验证错误: {e}")
            return False

    def on_start(self):
        # 验证参数
        if not self.validate_parameters():
            print("策略启动失败：参数验证不通过")
            return
            
        try:
            self.kline_generator = KLineGenerator(
                callback=self.callback,
                real_time_callback=self.real_time_callback,
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                style=self.params_map.kline_style
            )
            self.kline_generator.push_history_data()

            super().on_start()

            # 初始化状态
            self.signal_price = 0
            self.entry_price = 0.0
            self.max_favorable_price = 0.0

            self.buy_signal = False
            self.sell_signal = False
            self.cover_signal = False
            self.short_signal = False

            self.tick = None

            # 清空K线历史
            self.state_map.prev_kline = []
            self.state_map.reversal_detected = False
            self.state_map.current_trend = "neutral"
            self.state_map.position_direction = None
            self.state_map.stop_price = 0.0

            self.update_status_bar()
            print(f"增强交易策略启动成功 - 交易方向: {self.params_map.trade_direction}")
            
        except Exception as e:
            print(f"策略启动错误: {e}")

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 更新K线历史
        self.update_kline_history(kline)
        
        # 计算指标
        self.calc_indicators()

        # 计算趋势
        self.calc_trend()

        # 计算信号
        self.calc_signal(kline)

        # 信号执行
        self.exec_signal()

        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_indicators()
        self.calc_trend()

        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar() 
    def update_kline_history(self, kline: KLineData) -> None:
        """更新K线历史记录（最多保存3根）"""
        # 将K线数据转换为字典存储
        kline_dict = {
            "open": kline.open,
            "high": kline.high,
            "low": kline.low,
            "close": kline.close,
            "volume": kline.volume,
            "datetime": kline.datetime
        }
        
        # 添加到历史记录
        self.state_map.prev_kline.append(kline_dict)
        
        # 保持最多3根K线
        if len(self.state_map.prev_kline) > 3:
            self.state_map.prev_kline.pop(0)

    def calc_indicators(self) -> None:
        """计算技术指标：EMA(12), EMA(26), RSI(14), ATR(14)"""
        try:
            # 计算快速和慢速EMA
            ema_fast_array = self.kline_generator.producer.ema(self.params_map.ema_fast, array=True)
            ema_slow_array = self.kline_generator.producer.ema(self.params_map.ema_slow, array=True)
            
            if len(ema_fast_array) >= 2 and len(ema_slow_array) >= 2:
                # 保存前一周期值用于趋势变化检测
                self.prev_ema_fast = self.state_map.ema_fast
                self.prev_ema_slow = self.state_map.ema_slow
                
                # 更新当前值
                self.state_map.ema_fast = round(ema_fast_array[-1], 2)
                self.state_map.ema_slow = round(ema_slow_array[-1], 2)
            
            # 计算RSI
            rsi_array = self.kline_generator.producer.rsi(self.params_map.rsi_period, array=True)
            if len(rsi_array) >= 2:
                self.prev_rsi = self.state_map.rsi
                self.state_map.rsi = round(rsi_array[-1], 2)
            
            # 计算ATR
            atr_value, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
            self.state_map.atr = round(atr_value, 2)
            
        except Exception as e:
            # 处理指标计算失败的情况
            print(f"指标计算错误: {e}")
            # 保持之前的值不变，确保策略继续运行

    def calc_trend(self) -> None:
        """计算市场趋势：基于EMA交叉和RSI确认"""
        # 保存前一趋势用于变化检测
        self.prev_trend = self.state_map.current_trend
        
        # 趋势判断逻辑
        if self.state_map.ema_fast > self.state_map.ema_slow and self.state_map.rsi > 50:
            new_trend = "up"
        elif self.state_map.ema_fast < self.state_map.ema_slow and self.state_map.rsi < 50:
            new_trend = "down"
        else:
            new_trend = "neutral"
        
        # 更新趋势状态
        self.state_map.current_trend = new_trend
        
        # 检测趋势变化（用于自动模式的仓位切换）
        if self.prev_trend != new_trend and self.params_map.trade_direction == "auto":
            self.handle_trend_change(self.prev_trend, new_trend)

    def handle_trend_change(self, prev_trend: str, new_trend: str) -> None:
        """处理趋势变化时的仓位调整"""
        try:
            position = self.get_position(self.params_map.instrument_id)
            
            # 趋势从上升转为下降：平多开空
            if prev_trend == "up" and new_trend == "down" and position.net_position > 0:
                self.cover_signal = True  # 平多
                self.short_signal = True  # 开空
                print(f"趋势变化：{prev_trend} -> {new_trend}，准备平多开空")
                
            # 趋势从下降转为上升：平空开多
            elif prev_trend == "down" and new_trend == "up" and position.net_position < 0:
                self.sell_signal = True   # 平空
                self.buy_signal = True    # 开多
                print(f"趋势变化：{prev_trend} -> {new_trend}，准备平空开多")
                
        except Exception as e:
            print(f"处理趋势变化错误: {e}")

    def calc_signal(self, kline: KLineData) -> None:
        """计算交易信号：基于趋势分析和仓位管理，添加严格的信号过滤条件"""
        # 重置信号
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        
        position = self.get_position(self.params_map.instrument_id)
        
        # 检查反转模式（优先级最高）
        reversal_detected = self.check_reversal_pattern()
        if reversal_detected and position.net_position != 0:
            if position.net_position > 0:
                self.sell_signal = True  # 平多
                print(f"检测到反转模式，平多仓位")
            else:
                self.cover_signal = True  # 平空
                print(f"检测到反转模式，平空仓位")
            return
        
        # 检查是否有足够的历史数据进行信号计算
        if not self.has_sufficient_data():
            return
            
        # 检查趋势确认条件
        if not self.is_trend_confirmed():
            return
            
        # 检查EMA交叉信号（关键信号过滤条件）
        ema_cross_signal = self.check_ema_cross()
        if not ema_cross_signal:
            return
            
        # 检查RSI过滤条件
        if not self.check_rsi_filter():
            return
            
        # 获取信号方向
        signal_direction = self.get_signal_direction(self.state_map.current_trend)
        if not signal_direction:
            return
        
        # 执行交易逻辑
        if signal_direction == "buy":
            if position.net_position < 0:
                # 有空头仓位，先平空
                self.cover_signal = True
                print(f"EMA金叉确认，平空仓位")
            elif position.net_position == 0 and self.can_open_position("buy"):
                # 无仓位且可以开仓，开多
                self.buy_signal = True
                print(f"EMA金叉确认，开多仓位")
                
        elif signal_direction == "sell":
            if position.net_position > 0:
                # 有多头仓位，先平多
                self.sell_signal = True
                print(f"EMA死叉确认，平多仓位")
            elif position.net_position == 0 and self.can_open_position("sell"):
                # 无仓位且可以开仓，开空
                self.short_signal = True
                print(f"EMA死叉确认，开空仓位")
        
        # 设置交易价格
        self.long_price = self.short_price = kline.close
        
        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1
            
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2

    def exec_signal(self) -> None:
        """执行交易信号：包含仓位检查、止损检查和反转平仓"""
        self.signal_price = 0
        position = self.get_position(self.params_map.instrument_id)
        
        # 更新仓位方向状态
        self.update_position_direction()
        
        # 检查止损
        current_price = self.tick.last_price if self.tick else 0
        if current_price > 0 and self.check_stop_hit(current_price):
            # 触发止损，立即平仓
            if position.net_position > 0:
                self.signal_price = -self.short_price
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.short_price,
                        volume=position.net_position,
                        order_direction="sell"
                    )
            elif position.net_position < 0:
                self.signal_price = self.long_price
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.long_price,
                        volume=abs(position.net_position),
                        order_direction="buy"
                    )
            return
        
        # 更新追踪止损
        if position.net_position != 0 and current_price > 0:
            direction = "long" if position.net_position > 0 else "short"
            self.update_trailing_stop(current_price, direction)
        
        # 取消未成交订单
        if self.order_id is not None:
            try:
                self.cancel_order(self.order_id)
            except Exception as e:
                print(f"取消订单错误: {e}")
                self.order_id = None
        
        # 平仓信号处理
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
        
        # 开仓信号处理（检查最大持仓限制）
        if self.short_signal and self.can_open_position("sell"):
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
                # 设置初始止损
                if self.order_id:
                    self.entry_price = self.short_price
                    self.max_favorable_price = self.short_price
                    self.state_map.stop_price = self.calculate_initial_stop(self.short_price, "short")
                    
        elif self.buy_signal and self.can_open_position("buy"):
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                # 设置初始止损
                if self.order_id:
                    self.entry_price = self.long_price
                    self.max_favorable_price = self.long_price
                    self.state_map.stop_price = self.calculate_initial_stop(self.long_price, "long")

    def can_open_position(self, direction: str) -> bool:
        """检查是否可以开仓"""
        position = self.get_position(self.params_map.instrument_id)
        current_position_count = abs(position.net_position)
        
        # 检查最大持仓限制
        if current_position_count >= self.params_map.max_position:
            return False
        
        # 防止同方向重复开仓
        if direction == "buy" and position.net_position > 0:
            return False
        elif direction == "sell" and position.net_position < 0:
            return False
        
        return True

    def get_signal_direction(self, trend: str) -> Optional[str]:
        """根据趋势和参数获取信号方向"""
        if self.params_map.trade_direction == "buy":
            return "buy"
        elif self.params_map.trade_direction == "sell":
            return "sell"
        elif self.params_map.trade_direction == "auto":
            if trend == "up":
                return "buy"
            elif trend == "down":
                return "sell"
            else:
                return None
        return None

    def update_position_direction(self) -> None:
        """更新持仓方向状态"""
        position = self.get_position(self.params_map.instrument_id)
        
        if position.net_position > 0:
            self.state_map.position_direction = "long"
        elif position.net_position < 0:
            self.state_map.position_direction = "short"
        else:
            self.state_map.position_direction = None

    def calculate_initial_stop(self, entry_price: float, direction: str) -> float:
        """计算初始止损价格：开仓价 ± (ATR × 止损倍数)"""
        if self.state_map.atr <= 0:
            return 0.0
            
        stop_distance = self.state_map.atr * self.params_map.stop_loss_multiplier
        
        if direction == "long":
            return entry_price - stop_distance
        else:  # short
            return entry_price + stop_distance

    def update_trailing_stop(self, current_price: float, direction: str) -> None:
        """更新追踪止损"""
        if self.state_map.position_direction is None or self.entry_price == 0.0:
            return
            
        # 更新最有利价格
        if direction == "long":
            if current_price > self.max_favorable_price:
                self.max_favorable_price = current_price
                # 计算新的追踪止损价格
                profit = self.max_favorable_price - self.entry_price
                if profit > 0:
                    trail_distance = profit * self.params_map.trail_profit_ratio
                    new_stop = self.entry_price + trail_distance
                    # 止损只能向有利方向移动
                    if new_stop > self.state_map.stop_price:
                        self.state_map.stop_price = round(new_stop, 2)
        else:  # short
            if current_price < self.max_favorable_price:
                self.max_favorable_price = current_price
                # 计算新的追踪止损价格
                profit = self.entry_price - self.max_favorable_price
                if profit > 0:
                    trail_distance = profit * self.params_map.trail_profit_ratio
                    new_stop = self.entry_price - trail_distance
                    # 止损只能向有利方向移动
                    if new_stop < self.state_map.stop_price or self.state_map.stop_price == 0.0:
                        self.state_map.stop_price = round(new_stop, 2)

    def check_stop_hit(self, current_price: float) -> bool:
        """检查是否触及止损"""
        if self.state_map.stop_price == 0.0 or self.state_map.position_direction is None:
            return False
            
        if self.state_map.position_direction == "long":
            return current_price <= self.state_map.stop_price
        else:  # short
            return current_price >= self.state_map.stop_price

    def check_reversal_pattern(self) -> bool:
        """检查反转模式：连续3根反向K线"""
        if len(self.state_map.prev_kline) < 3 or self.state_map.position_direction is None:
            return False
        
        # 获取最近3根K线
        recent_klines = self.state_map.prev_kline[-3:]
        
        if self.state_map.position_direction == "long":
            # 多头仓位：检查3根连续阴线（close < open）
            bearish_count = sum(1 for k in recent_klines if k["close"] < k["open"])
            reversal_detected = bearish_count == 3
        else:  # short
            # 空头仓位：检查3根连续阳线（close > open）
            bullish_count = sum(1 for k in recent_klines if k["close"] > k["open"])
            reversal_detected = bullish_count == 3
        
        # 更新反转检测状态
        self.state_map.reversal_detected = reversal_detected
        return reversal_detected

    def has_sufficient_data(self) -> bool:
        """检查是否有足够的历史数据进行信号计算"""
        try:
            # 检查EMA数据
            ema_fast_array = self.kline_generator.producer.ema(self.params_map.ema_fast, array=True)
            ema_slow_array = self.kline_generator.producer.ema(self.params_map.ema_slow, array=True)
            
            # 需要至少有慢速EMA周期+5根K线的数据
            min_required = max(self.params_map.ema_slow, self.params_map.rsi_period) + 5
            
            return (len(ema_fast_array) >= min_required and 
                    len(ema_slow_array) >= min_required and
                    self.state_map.ema_fast > 0 and 
                    self.state_map.ema_slow > 0)
        except:
            return False

    def is_trend_confirmed(self) -> bool:
        """检查趋势是否得到确认（连续3根K线趋势一致）"""
        if len(self.state_map.prev_kline) < 3:
            return False
            
        recent_klines = self.state_map.prev_kline[-3:]
        
        # 检查价格趋势一致性
        if self.state_map.current_trend == "up":
            # 上升趋势：最近3根K线的收盘价应该整体向上
            closes = [k["close"] for k in recent_klines]
            return closes[-1] > closes[0]  # 最新收盘价高于3根K线前的收盘价
            
        elif self.state_map.current_trend == "down":
            # 下降趋势：最近3根K线的收盘价应该整体向下
            closes = [k["close"] for k in recent_klines]
            return closes[-1] < closes[0]  # 最新收盘价低于3根K线前的收盘价
            
        return False

    def check_ema_cross(self) -> bool:
        """检查EMA交叉信号（关键过滤条件）"""
        # 需要有前一周期的数据进行比较
        if self.prev_ema_fast == 0 or self.prev_ema_slow == 0:
            return False
            
        # 当前周期EMA状态
        current_fast_above = self.state_map.ema_fast > self.state_map.ema_slow
        # 前一周期EMA状态  
        prev_fast_above = self.prev_ema_fast > self.prev_ema_slow
        
        # 检查是否发生交叉
        cross_occurred = current_fast_above != prev_fast_above
        
        if cross_occurred:
            if current_fast_above:
                print(f"检测到EMA金叉: 快线{self.state_map.ema_fast} > 慢线{self.state_map.ema_slow}")
            else:
                print(f"检测到EMA死叉: 快线{self.state_map.ema_fast} < 慢线{self.state_map.ema_slow}")
                
        return cross_occurred

    def check_rsi_filter(self) -> bool:
        """检查RSI过滤条件"""
        # RSI极值过滤：避免在超买超卖区域开仓
        if self.state_map.current_trend == "up":
            # 上升趋势中，RSI不应该在超买区域（>70）
            return self.state_map.rsi < 70
        elif self.state_map.current_trend == "down":
            # 下降趋势中，RSI不应该在超卖区域（<30）
            return self.state_map.rsi > 30
            
        return True