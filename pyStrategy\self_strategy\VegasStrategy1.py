from typing import Literal, Dict, Any
import logging
import numpy as np
import math
from datetime import datetime, timedelta

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型 - 专业交易员版本"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="D1", title="K线周期")
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 维加斯隧道核心参数
    vegas_fast: int = Field(default=144, title="维加斯快线周期")
    vegas_slow: int = Field(default=169, title="维加斯慢线周期")
    
    # 多时间框架确认系统
    confirm_timeframe: str = Field(default="H4", title="确认时间框架")
    tunnel_width_threshold: float = Field(default=0.005, title="隧道宽度阈值(百分比)")
    
    # 多重EMA系统
    ema_fast: int = Field(default=12, title="快速EMA周期")
    ema_medium: int = Field(default=26, title="中速EMA周期") 
    ema_slow: int = Field(default=50, title="慢速EMA周期")
    
    # MACD参数
    macd_fast: int = Field(default=12, title="MACD快线")
    macd_slow: int = Field(default=26, title="MACD慢线")
    macd_signal: int = Field(default=9, title="MACD信号线")
    
    # RSI参数
    rsi_period: int = Field(default=14, title="RSI周期")
    rsi_overbought: float = Field(default=70, title="RSI超买线")
    rsi_oversold: float = Field(default=30, title="RSI超卖线")
    
    # 布林带参数
    bb_period: int = Field(default=20, title="布林带周期")
    bb_std: float = Field(default=2.0, title="布林带标准差倍数")
    
    # 成交量确认
    volume_ma_period: int = Field(default=20, title="成交量均线周期")
    volume_threshold: float = Field(default=1.2, title="成交量放大倍数")
    avg_volume_period: int = Field(default=30, title="平均成交量周期")
    
    # ADX趋势强度
    adx_period: int = Field(default=14, title="ADX周期")
    adx_threshold: float = Field(default=25, title="ADX趋势阈值")
    
    # ATR参数
    atr_period: int = Field(default=14, title="ATR周期")
    stop_atr_multiplier: float = Field(default=2.0, title="止损ATR倍数")
    
    # 信号融合权重
    ema_weight: float = Field(default=0.25, title="EMA信号权重")
    macd_weight: float = Field(default=0.20, title="MACD信号权重")
    rsi_weight: float = Field(default=0.15, title="RSI信号权重")
    bb_weight: float = Field(default=0.15, title="布林带信号权重")
    volume_weight: float = Field(default=0.10, title="成交量信号权重")
    tunnel_weight: float = Field(default=0.15, title="隧道强度权重")
    
    # 信号阈值
    signal_threshold: float = Field(default=0.6, title="综合信号阈值")
    
    # 市场状态权重
    trend_weight: float = Field(default=0.5, title="趋势权重")
    volatility_weight: float = Field(default=0.3, title="波动率权重")
    momentum_weight: float = Field(default=0.2, title="动量权重")
    
    # 市场状态阈值
    strong_bull_threshold: float = Field(default=0.3, title="强多头市场阈值")
    strong_bear_threshold: float = Field(default=-0.3, title="强空头市场阈值")
    high_volatility_threshold: float = Field(default=0.7, title="高波动市场阈值")
    energy_threshold_high_vol: float = Field(default=60, title="高波动时能量阈值")


class State(BaseState):
    """状态映射模型 - 专业交易员版本"""
    # 维加斯隧道核心指标
    vegas_fast: float = Field(default=0, title="维加斯快线")
    vegas_slow: float = Field(default=0, title="维加斯慢线")
    
    # 多时间框架确认
    tunnel_strength: float = Field(default=0, title="隧道强度")
    tunnel_width: float = Field(default=0, title="隧道宽度")
    
    # 价格隧道位置能量指标
    price_energy: float = Field(default=0, title="价格能量")
    avg_volume: float = Field(default=0, title="平均成交量")
    
    # 智能止盈回撤保护
    peak_price: float = Field(default=0, title="峰值价格")
    drawdown_threshold: float = Field(default=0, title="回撤阈值")
    
    # 多重EMA系统
    ema_fast: float = Field(default=0, title="快速EMA")
    ema_medium: float = Field(default=0, title="中速EMA")
    ema_slow: float = Field(default=0, title="慢速EMA")
    
    # MACD指标
    macd_line: float = Field(default=0, title="MACD线")
    macd_signal: float = Field(default=0, title="MACD信号线")
    macd_histogram: float = Field(default=0, title="MACD柱状图")
    
    # RSI指标
    rsi: float = Field(default=50, title="RSI值")
    
    # 布林带指标
    bb_upper: float = Field(default=0, title="布林带上轨")
    bb_middle: float = Field(default=0, title="布林带中轨")
    bb_lower: float = Field(default=0, title="布林带下轨")
    bb_percent: float = Field(default=0, title="布林带百分比")
    
    # 成交量指标
    volume_ma: float = Field(default=0, title="成交量均线")
    volume_ratio: float = Field(default=1, title="成交量比率")
    
    # ADX趋势强度
    adx: float = Field(default=0, title="ADX值")
    di_plus: float = Field(default=0, title="DI+")
    di_minus: float = Field(default=0, title="DI-")
    
    # ATR
    atr: float = Field(default=0, title="ATR值")
    
    # 市场状态感知
    market_state: Dict[str, float] = Field(default_factory=dict, title="市场状态")
    trend_score: float = Field(default=0, title="趋势得分")
    volatility_score: float = Field(default=0, title="波动率得分")
    momentum_score: float = Field(default=0, title="动量得分")
    composite_state: float = Field(default=0, title="综合市场状态")
    
    # 综合信号
    bull_signal_strength: float = Field(default=0, title="多头信号强度")
    bear_signal_strength: float = Field(default=0, title="空头信号强度")
    
    # 订单管理
    pending_order: bool = Field(default=False, title="是否有挂单")
    kline_count: int = Field(default=0, title="K线计数")
    
    # 历史数据
    high_prices: list = Field(default_factory=list, title="最高价历史")
    low_prices: list = Field(default_factory=list, title="最低价历史")
    close_prices: list = Field(default_factory=list, title="收盘价历史")
    open_prices: list = Field(default_factory=list, title="开盘价历史")
    volume_data: list = Field(default_factory=list, title="成交量历史")
    
    # 多时间框架数据缓存
    confirm_tf_data: list = Field(default_factory=list, title="确认时间框架数据")


class VegasStrategy1(BaseStrategy):
    """维加斯隧道交易法策略 - 专业交易员版本
    
    基于专业交易员最佳实践的全面增强版本：
    1. 多时间框架隧道确认系统
    2. 价格隧道位置能量指标
    3. 智能止盈回撤保护系统
    4. 市场状态感知引擎
    5. 基于市场状态的信号过滤
    """
    
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()

        # 信号标志
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False

        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 初始化交易价格
        self.long_price = 0
        self.short_price = 0
        
        # 设置日志
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
        # 多时间框架数据管理
        self.confirm_tf_generator = None

    @property
    def main_indicator(self) -> dict[str, float]:
        """主图指标"""
        return {
            f"Vegas_Fast": self.state_map.vegas_fast,
            f"Vegas_Slow": self.state_map.vegas_slow,
            f"EMA{self.params_map.ema_fast}": self.state_map.ema_fast,
            f"EMA{self.params_map.ema_medium}": self.state_map.ema_medium,
            f"EMA{self.params_map.ema_slow}": self.state_map.ema_slow,
            "BB_Upper": self.state_map.bb_upper,
            "BB_Middle": self.state_map.bb_middle,
            "BB_Lower": self.state_map.bb_lower,
            "Peak_Price": self.state_map.peak_price
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "MACD": self.state_map.macd_line,
            "MACD_Signal": self.state_map.macd_signal,
            "MACD_Hist": self.state_map.macd_histogram,
            "RSI": self.state_map.rsi,
            "ADX": self.state_map.adx,
            "Tunnel_Strength": self.state_map.tunnel_strength,
            "Price_Energy": self.state_map.price_energy,
            "Bull_Strength": self.state_map.bull_signal_strength,
            "Bear_Strength": self.state_map.bear_signal_strength,
            "Market_State": self.state_map.composite_state
        }   
    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)
        
        # 智能止盈回撤保护检查
        if self.trading and not self.state_map.pending_order:
            self.update_profit_protection(tick.last_price)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None
        self.state_map.pending_order = False
        self.logger.info(f"订单已撤销: {order.order_id}")

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交推送回调"""
        super().on_trade(trade, log)
        self.order_id = None
        self.state_map.pending_order = False
        
        action = "开仓" if trade.offset == "open" else "平仓"
        direction = "买入" if trade.direction == "buy" else "卖出"
        
        # 记录详细成交信息
        self.logger.info(f"{action}{direction}: 价格={trade.price}, 数量={trade.volume}, "
                        f"能量值={self.state_map.price_energy:.2f}, "
                        f"市场状态={self.state_map.composite_state:.3f}")
        
        # 开仓时初始化止盈保护
        if trade.offset == "open":
            self.state_map.peak_price = trade.price
            self._update_drawdown_threshold()
        
        if trade.offset == "close":
            self._check_open_signal_after_close()

    def on_start(self):
        # 主时间框架K线生成器
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        
        # 添加K线窗口初始化
        from uiKLine import KLineWidget
        from PyQt5.QtWidgets import QWidget

        class IndicatorParent(QWidget):
            def __init__(self, main_indicator, parent=None):
                super().__init__(parent)
                self.main_indicator = main_indicator

        self.indicator_parent = IndicatorParent(self.main_indicator)
        self.widget = KLineWidget(parent=self.indicator_parent)
        self.widget.init_ui()
        
        # 确认时间框架K线生成器
        self.confirm_tf_generator = KLineGenerator(
            callback=self._confirm_tf_callback,
            real_time_callback=None,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.confirm_timeframe
        )
        
        self.kline_generator.push_history_data()
        self.confirm_tf_generator.push_history_data()

        super().on_start()
        self._reset_state()
        
        self.logger.info(f"专业交易员策略启动: {self.params_map.instrument_id}, "
                        f"主周期={self.params_map.kline_style}, "
                        f"确认周期={self.params_map.confirm_timeframe}")
        self.update_status_bar()

    def on_stop(self):
        super().on_stop()
        self.logger.info("策略停止")

    def _reset_state(self):
        """重置策略状态"""
        self.signal_price = 0
        self.long_price = 0
        self.short_price = 0

        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

        self.tick = None
        self.state_map.pending_order = False
        self.state_map.kline_count = 0
        
        # 重置历史数据
        self.state_map.high_prices = []
        self.state_map.low_prices = []
        self.state_map.close_prices = []
        self.state_map.open_prices = []
        self.state_map.volume_data = []
        self.state_map.confirm_tf_data = []
        
        # 初始化市场状态
        self.state_map.market_state = {
            'trend': 0.0,
            'volatility': 0.0,
            'momentum': 0.0,
            'composite': 0.0
        }

    def callback(self, kline: KLineData) -> None:
        """主时间框架K线回调"""
        try:
            # 计算所有技术指标
            self.calc_all_indicators(kline)
            
            # 多时间框架隧道确认
            self.calc_tunnel_confirmation()
            
            # 价格隧道位置能量指标
            self.calc_price_energy(kline)
            
            # 市场状态感知分析
            self.market_state_analysis()

            # 如果有挂单，跳过信号处理
            if self.state_map.pending_order:
                self.logger.debug("存在挂单，跳过信号处理")
                self._update_display(kline)
                return

            # 计算综合信号
            self.calc_composite_signals_enhanced(kline)

            # 信号执行
            self.exec_signal_enhanced()

            # 更新显示
            self._update_display(kline)

        except Exception as e:
            self.logger.error(f"K线回调异常: {e}")

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        try:
            self.calc_all_indicators(kline, is_realtime=True)
            self.calc_price_energy(kline, is_realtime=True)
            self._update_display(kline)
        except Exception as e:
            self.logger.error(f"实时回调异常: {e}")

    def _confirm_tf_callback(self, kline: KLineData) -> None:
        """确认时间框架K线回调"""
        try:
            # 存储确认时间框架数据
            self.state_map.confirm_tf_data.append({
                'close': kline.close,
                'high': kline.high,
                'low': kline.low,
                'timestamp': getattr(kline, 'timestamp', datetime.now())
            })
            
            # 保持数据长度
            max_confirm_history = max(self.params_map.vegas_fast, self.params_map.vegas_slow) + 20
            if len(self.state_map.confirm_tf_data) > max_confirm_history:
                self.state_map.confirm_tf_data = self.state_map.confirm_tf_data[-max_confirm_history:]
                
        except Exception as e:
            self.logger.error(f"确认时间框架回调异常: {e}")

    def calc_all_indicators(self, kline: KLineData, is_realtime: bool = False) -> None:
        """计算所有技术指标"""
        close_price = kline.close
        high_price = kline.high
        low_price = kline.low
        open_price = getattr(kline, 'open', close_price)
        volume = getattr(kline, 'volume', 0)
        
        if not is_realtime:
            self.state_map.kline_count += 1
            
            # 更新历史数据
            self.state_map.high_prices.append(high_price)
            self.state_map.low_prices.append(low_price)
            self.state_map.close_prices.append(close_price)
            self.state_map.open_prices.append(open_price)
            self.state_map.volume_data.append(volume)
            
            # 保持历史长度
            max_history = max(self.params_map.vegas_slow, self.params_map.bb_period, 
                            self.params_map.adx_period, self.params_map.avg_volume_period) + 20
            if len(self.state_map.close_prices) > max_history:
                self.state_map.high_prices = self.state_map.high_prices[-max_history:]
                self.state_map.low_prices = self.state_map.low_prices[-max_history:]
                self.state_map.close_prices = self.state_map.close_prices[-max_history:]
                self.state_map.open_prices = self.state_map.open_prices[-max_history:]
                self.state_map.volume_data = self.state_map.volume_data[-max_history:]
        
        if not is_realtime and len(self.state_map.close_prices) >= 2:
            # 计算维加斯隧道
            self._calc_vegas_tunnel()
            
            # 计算多重EMA系统
            self._calc_ema_system()
            
            # 计算其他技术指标
            self._calc_macd()
            self._calc_rsi()
            self._calc_bollinger_bands()
            self._calc_volume_indicators()
            self._calc_adx()
            self._calc_atr()

    def _calc_vegas_tunnel(self):
        """计算维加斯隧道核心指标"""
        closes = np.array(self.state_map.close_prices)
        
        # 计算维加斯隧道EMA
        self.state_map.vegas_fast = self._ema(closes, self.params_map.vegas_fast)
        self.state_map.vegas_slow = self._ema(closes, self.params_map.vegas_slow)

    def calc_tunnel_confirmation(self):
        """多时间框架隧道确认系统"""
        if len(self.state_map.confirm_tf_data) < max(self.params_map.vegas_fast, self.params_map.vegas_slow):
            self.state_map.tunnel_strength = 0
            self.state_map.tunnel_width = 0
            return
        
        try:
            # 提取确认时间框架的收盘价
            confirm_closes = np.array([data['close'] for data in self.state_map.confirm_tf_data])
            
            # 计算确认时间框架的维加斯隧道
            confirm_fast = self._ema(confirm_closes, self.params_map.vegas_fast)
            confirm_slow = self._ema(confirm_closes, self.params_map.vegas_slow)
            
            current_price = confirm_closes[-1]
            
            # 计算隧道宽度百分比
            self.state_map.tunnel_width = abs(confirm_fast - confirm_slow) / current_price
            
            # 判断隧道强度
            if self.state_map.tunnel_width > self.params_map.tunnel_width_threshold:
                if confirm_fast > confirm_slow:
                    self.state_map.tunnel_strength = 1.0  # 强多头
                else:
                    self.state_map.tunnel_strength = -1.0  # 强空头
                    
                self.logger.debug(f"隧道确认: 强度={self.state_map.tunnel_strength}, "
                                f"宽度={self.state_map.tunnel_width:.4f}")
            else:
                self.state_map.tunnel_strength = 0  # 隧道太窄，无明确方向
                
        except Exception as e:
            self.logger.error(f"隧道确认计算异常: {e}")
            self.state_map.tunnel_strength = 0
            self.state_map.tunnel_width = 0

    def calc_price_energy(self, kline: KLineData, is_realtime: bool = False):
        """价格隧道位置能量指标"""
        if len(self.state_map.close_prices) < 2:
            self.state_map.price_energy = 0
            return
        
        try:
            close_price = kline.close
            open_price = getattr(kline, 'open', close_price)
            volume = getattr(kline, 'volume', 0)
            
            # 计算平均成交量
            if not is_realtime and len(self.state_map.volume_data) >= self.params_map.avg_volume_period:
                self.state_map.avg_volume = np.mean(self.state_map.volume_data[-self.params_map.avg_volume_period:])
            
            if self.state_map.avg_volume == 0:
                self.state_map.price_energy = 0
                return
            
            # 计算位置 = (收盘价-慢线)/(快线-慢线)
            if abs(self.state_map.vegas_fast - self.state_map.vegas_slow) < 1e-6:
                position = 0
            else:
                position = (close_price - self.state_map.vegas_slow) / (self.state_map.vegas_fast - self.state_map.vegas_slow)
            
            # 计算动量 = 收盘价/开盘价 - 1
            if open_price > 0:
                momentum = close_price / open_price - 1
            else:
                momentum = 0
            
            # 计算成交量因子 = log(当前成交量/平均成交量 + 1)
            volume_factor = math.log(volume / self.state_map.avg_volume + 1) if self.state_map.avg_volume > 0 else 0
            
            # 计算能量 = 位置 × 动量 × 成交量因子
            self.state_map.price_energy = position * momentum * volume_factor * 100  # 放大100倍便于显示
            
            if not is_realtime:
                self.logger.debug(f"价格能量: {self.state_map.price_energy:.2f}, "
                                f"位置={position:.3f}, 动量={momentum:.4f}, 成交量因子={volume_factor:.3f}")
                
        except Exception as e:
            self.logger.error(f"价格能量计算异常: {e}")
            self.state_map.price_energy = 0

    def update_profit_protection(self, current_price: float):
        """智能止盈回撤保护系统"""
        try:
            position = self.get_position(self.params_map.instrument_id)
            
            if position.net_position == 0:
                return  # 无持仓
            
            # 更新峰值价格
            if position.net_position > 0:  # 多头持仓
                if current_price > self.state_map.peak_price:
                    self.state_map.peak_price = current_price
                    self._update_drawdown_threshold()
                
                # 检查回撤止盈
                if current_price <= (self.state_map.peak_price - self.state_map.drawdown_threshold):
                    self.logger.info(f"多头止盈触发: 当前价格={current_price}, "
                                   f"峰值={self.state_map.peak_price}, "
                                   f"回撤阈值={self.state_map.drawdown_threshold}")
                    self._trigger_profit_taking("sell")
                    
            elif position.net_position < 0:  # 空头持仓
                if current_price < self.state_map.peak_price or self.state_map.peak_price == 0:
                    self.state_map.peak_price = current_price
                    self._update_drawdown_threshold()
                
                # 检查回撤止盈
                if current_price >= (self.state_map.peak_price + self.state_map.drawdown_threshold):
                    self.logger.info(f"空头止盈触发: 当前价格={current_price}, "
                                   f"峰值={self.state_map.peak_price}, "
                                   f"回撤阈值={self.state_map.drawdown_threshold}")
                    self._trigger_profit_taking("buy")
                    
        except Exception as e:
            self.logger.error(f"止盈保护更新异常: {e}")

    def _update_drawdown_threshold(self):
        """更新动态回撤阈值"""
        # 动态回撤阈值 = ATR × (2 当|能量值|>70 否则 1)
        energy_multiplier = 2 if abs(self.state_map.price_energy) > 70 else 1
        self.state_map.drawdown_threshold = self.state_map.atr * energy_multiplier
        
        self.logger.debug(f"回撤阈值更新: {self.state_map.drawdown_threshold:.4f}, "
                         f"ATR={self.state_map.atr:.4f}, 能量倍数={energy_multiplier}")

    def _trigger_profit_taking(self, direction: str):
        """触发止盈平仓"""
        if self.state_map.pending_order:
            return  # 已有挂单
        
        try:
            position = self.get_position(self.params_map.instrument_id)
            
            if position.net_position == 0:
                return
            
            # 获取交易价格
            self.long_price, self.short_price = self.get_trade_price()
            
            if direction == "sell" and position.net_position > 0:
                # 平多仓
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                
            elif direction == "buy" and position.net_position < 0:
                # 平空仓
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                
        except Exception as e:
            self.logger.error(f"止盈平仓异常: {e}")

    def market_state_analysis(self):
        """市场状态感知引擎"""
        try:
            # 1. 趋势得分：3个时间框架趋势方向一致性
            trend_score = self._calc_trend_score()
            
            # 2. 波动率得分：ATR/收盘价 标准化
            volatility_score = self._calc_volatility_score()
            
            # 3. 动量得分：RSI位置标准化
            momentum_score = self._calc_momentum_score()
            
            # 更新状态
            self.state_map.trend_score = trend_score
            self.state_map.volatility_score = volatility_score
            self.state_map.momentum_score = momentum_score
            
            # 综合状态 = 0.5×趋势 + 0.3×波动 + 0.2×动量
            self.state_map.composite_state = (
                trend_score * self.params_map.trend_weight +
                volatility_score * self.params_map.volatility_weight +
                momentum_score * self.params_map.momentum_weight
            )
            
            # 更新市场状态字典
            self.state_map.market_state = {
                'trend': trend_score,
                'volatility': volatility_score,
                'momentum': momentum_score,
                'composite': self.state_map.composite_state
            }
            
            self.logger.debug(f"市场状态: 趋势={trend_score:.3f}, 波动={volatility_score:.3f}, "
                            f"动量={momentum_score:.3f}, 综合={self.state_map.composite_state:.3f}")
            
        except Exception as e:
            self.logger.error(f"市场状态分析异常: {e}")

    def _calc_trend_score(self) -> float:
        """计算趋势得分"""
        try:
            scores = []
            
            # 主时间框架趋势
            if self.state_map.ema_fast > self.state_map.ema_slow:
                scores.append(1.0)
            elif self.state_map.ema_fast < self.state_map.ema_slow:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            # 维加斯隧道趋势
            if self.state_map.vegas_fast > self.state_map.vegas_slow:
                scores.append(1.0)
            elif self.state_map.vegas_fast < self.state_map.vegas_slow:
                scores.append(-1.0)
            else:
                scores.append(0.0)
            
            # 确认时间框架趋势
            scores.append(self.state_map.tunnel_strength)
            
            # 计算一致性得分
            return np.mean(scores)
            
        except Exception as e:
            self.logger.error(f"趋势得分计算异常: {e}")
            return 0.0

    def _calc_volatility_score(self) -> float:
        """计算波动率得分"""
        try:
            if self.state_map.atr == 0 or len(self.state_map.close_prices) == 0:
                return 0.0
            
            current_price = self.state_map.close_prices[-1]
            volatility_ratio = self.state_map.atr / current_price
            
            # 标准化到-1到1范围（假设正常波动率在0.01-0.05之间）
            normalized = (volatility_ratio - 0.03) / 0.02
            return max(-1.0, min(1.0, normalized))
            
        except Exception as e:
            self.logger.error(f"波动率得分计算异常: {e}")
            return 0.0

    def _calc_momentum_score(self) -> float:
        """计算动量得分"""
        try:
            # RSI位置标准化到-1到1
            return (self.state_map.rsi - 50) / 50
            
        except Exception as e:
            self.logger.error(f"动量得分计算异常: {e}")
            return 0.0

    def _calc_bb_signal_score(self) -> float:
        """计算布林带信号评分"""
        if self.state_map.bb_percent >= 0.8:
            return -0.8
        elif self.state_map.bb_percent <= 0.2:
            return 0.8
        else:
            return (self.state_map.bb_percent - 0.5) * 2 * 0.3

    def _calc_volume_signal_score(self) -> float:
        """计算成交量信号评分"""
        if self.state_map.volume_ratio >= self.params_map.volume_threshold:
            return min(1.0, (self.state_map.volume_ratio - 1) / self.params_map.volume_threshold)
        else:
            return -0.3

    def _reset_signals(self):
        """重置所有信号"""
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

    def get_trade_price(self) -> tuple[float, float]:
        """统一处理价格获取"""
        if self.tick:
            if self.params_map.price_type == "D1":
                return self.tick.ask_price1, self.tick.bid_price1
            else:
                return self.tick.ask_price2, self.tick.bid_price2
        else:
            close_price = getattr(self, '_last_close', 0)
            return close_price, close_price

    def _update_trade_prices(self, kline: KLineData):
        """更新交易价格"""
        self._last_close = kline.close
        self.long_price, self.short_price = self.get_trade_price()

    def exec_signal_enhanced(self):
        """增强信号执行系统"""
        self.signal_price = 0

        try:
            position = self.get_position(self.params_map.instrument_id)
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            return

        # 优先处理平仓信号
        if self._execute_close_signals(position):
            return

        # 处理开仓信号
        if position.net_position == 0:
            self._execute_open_signals()

    def _execute_close_signals(self, position) -> bool:
        """执行平仓信号"""
        # 平多仓
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                self.logger.info(f"平多仓: 价格={self.short_price}, 数量={position.net_position}, "
                               f"信号强度={abs(self.state_map.bear_signal_strength):.3f}")
            return True
        
        # 平空仓
        if position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                self.logger.info(f"平空仓: 价格={self.long_price}, 数量={abs(position.net_position)}, "
                               f"信号强度={self.state_map.bull_signal_strength:.3f}")
            return True
        
        return False

    def _execute_open_signals(self):
        """执行开仓信号"""
        # 开空仓
        if self.short_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                self.logger.info(f"开空仓: 价格={self.short_price}, 数量={self.params_map.order_volume}, "
                               f"信号强度={abs(self.state_map.bear_signal_strength):.3f}")
        
        # 开多仓
        elif self.buy_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                self.logger.info(f"开多仓: 价格={self.long_price}, 数量={self.params_map.order_volume}, "
                               f"信号强度={self.state_map.bull_signal_strength:.3f}")

    def _check_open_signal_after_close(self):
        """平仓成交后检查开仓信号"""
        try:
            position = self.get_position(self.params_map.instrument_id)
            
            if position.net_position == 0:
                if self.buy_signal and self.params_map.trade_direction == "buy":
                    self._execute_open_signals()
                elif self.short_signal and self.params_map.trade_direction == "sell":
                    self._execute_open_signals()
                    
        except Exception as e:
            self.logger.error(f"平仓后检查开仓信号失败: {e}")

    def _update_display(self, kline: KLineData):
        """更新显示"""
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()