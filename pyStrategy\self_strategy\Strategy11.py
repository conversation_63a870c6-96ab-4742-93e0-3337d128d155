"""
Strategy1 - 群论增强型模糊推理交易策略
基于无限易Pro交易软件的完整实现

核心功能:
- Hull移动平均线趋势判断 (完整Python实现)
- STC动量确认 (完整Python实现)
- 群论增强模糊推理系统 (内嵌群论运算)
- K线形态识别
- 智能信号融合

技术特点:
- 完整实现HULL和STC技术指标
- 群论数学工具内嵌于模糊推理决策
- 符合无限易Pro部署规范
- 单文件完整实现，无外部依赖
"""

from typing import Literal, Dict, List, Optional, Tuple, Any
import numpy as np
import math

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    
    # 技术指标参数
    hull_period: int = Field(default=9, title="HULL周期")
    stc_fast: int = Field(default=23, title="STC快周期")
    stc_slow: int = Field(default=50, title="STC慢周期")
    stc_cycle: int = Field(default=10, title="STC循环周期")
    
    # 交易参数
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 自适应风险控制参数（剔除固定止盈止损设置）
    adaptive_risk_enabled: bool = Field(default=True, title="启用自适应风险控制")
    risk_sensitivity: float = Field(default=0.5, title="风险敏感度")
    
    # 模糊系统参数
    fuzzy_sensitivity: float = Field(default=0.5, title="模糊系统敏感度")
    
    # 信号阈值参数
    signal_threshold: float = Field(default=0.55, title="信号阈值")
    confidence_threshold: float = Field(default=0.6, title="置信度阈值")


class State(BaseState):
    """状态映射模型"""
    # HULL指标状态
    hull_value: float = Field(default=0, title="HULL当前值")
    hull_prev: float = Field(default=0, title="HULL前值")
    hull_trend: str = Field(default="NEUTRAL", title="HULL趋势")
    
    # STC指标状态
    stc_value: float = Field(default=0, title="STC当前值")
    stc_signal: float = Field(default=0, title="STC信号线")
    stc_trend: str = Field(default="NEUTRAL", title="STC趋势")
    
    # 模糊系统状态
    fuzzy_signal: float = Field(default=0, title="模糊信号值")
    fuzzy_confidence: float = Field(default=0, title="模糊置信度")
    fuzzy_action: str = Field(default="HOLD", title="模糊行动建议")
    
    # 实时预测状态（仅在持仓时激活）
    prediction_active: bool = Field(default=False, title="预测功能激活状态")
    predicted_price: float = Field(default=0, title="预测价格")
    prediction_confidence: float = Field(default=0, title="预测置信度")
    prediction_horizon: int = Field(default=5, title="预测时间窗口")
    
    # 自适应风险控制状态
    adaptive_stop_loss: float = Field(default=0, title="自适应止损价格")
    adaptive_take_profit: float = Field(default=0, title="自适应止盈价格")
    risk_level: float = Field(default=0, title="当前风险水平")
    
    # 综合决策状态
    final_signal: float = Field(default=0, title="最终信号强度")
    signal_confidence: float = Field(default=0, title="信号置信度")
    trading_action: str = Field(default="HOLD", title="交易行动")


class Strategy11(BaseStrategy):
    """群论增强型模糊推理交易策略"""
    
    def __init__(self):
        super().__init__()
        
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.short_signal: bool = False
        self.cover_signal: bool = False
        
        # 价格数据
        self.tick: TickData = None
        
        # 订单管理
        self.order_id = None
        self.signal_price = 0
        
        # HULL指标数据存储
        self.hull_prices = []
        self.hull_wma_half = []
        self.hull_wma_full = []
        self.hull_values = []
        
        # STC指标数据存储
        self.stc_prices = []
        self.stc_macd_values = []
        self.stc_stoch1_values = []
        self.stc_stoch2_values = []
        self.stc_signal_line = 0.0
        
        # 实时预测系统（仅在持仓时激活）
        self.prediction_system = None
        self.prediction_history = []
        
        # 自适应风险控制系统
        self.adaptive_risk_controller = None
        
        # 群论运算缓存
        self.group_cache = {}
        
        # 模糊推理规则
        self.fuzzy_rules = self._initialize_fuzzy_rules()
        
        # 初始化智能化组件
        self._initialize_intelligent_components()
    
    def _initialize_fuzzy_rules(self) -> List[Dict]:
        """初始化模糊推理规则（内嵌群论运算）"""
        return [
            # 强烈看涨信号 - 群论对称性增强
            {"hull": "bullish", "stc": "bullish", "pattern": "bullish", "output": "strong_buy", "weight": 1.0, "group_factor": 1.2},
            {"hull": "bullish", "stc": "bullish", "pattern": "neutral", "output": "buy", "weight": 0.8, "group_factor": 1.1},
            
            # 看涨信号 - 群论不变量支持
            {"hull": "bullish", "stc": "neutral", "pattern": "bullish", "output": "buy", "weight": 0.7, "group_factor": 1.0},
            {"hull": "neutral", "stc": "bullish", "pattern": "bullish", "output": "buy", "weight": 0.6, "group_factor": 0.9},
            
            # 强烈看跌信号 - 群论对称性增强
            {"hull": "bearish", "stc": "bearish", "pattern": "bearish", "output": "strong_sell", "weight": 1.0, "group_factor": 1.2},
            {"hull": "bearish", "stc": "bearish", "pattern": "neutral", "output": "sell", "weight": 0.8, "group_factor": 1.1},
            
            # 看跌信号 - 群论不变量支持
            {"hull": "bearish", "stc": "neutral", "pattern": "bearish", "output": "sell", "weight": 0.7, "group_factor": 1.0},
            {"hull": "neutral", "stc": "bearish", "pattern": "bearish", "output": "sell", "weight": 0.6, "group_factor": 0.9},
            
            # 中性信号
            {"hull": "neutral", "stc": "neutral", "pattern": "neutral", "output": "hold", "weight": 1.0, "group_factor": 1.0},
        ]
    
    def _initialize_intelligent_components(self):
        """初始化智能化组件"""
        # 实时预测系统（基于群论增强）
        self.prediction_system = self.GroupTheoryPredictionSystem()
        
        # 自适应风险控制系统
        self.adaptive_risk_controller = self.AdaptiveRiskController()
    
    class GroupTheoryPredictionSystem:
        """群论增强的实时预测系统（仅在持仓时激活）"""
        
        def __init__(self):
            self.is_active = False
            self.prediction_window = 5
            self.price_memory = []
            self.group_features_memory = []
            self.prediction_accuracy_history = []
            
        def activate(self, current_position):
            """激活预测系统（仅在持仓时）"""
            self.is_active = current_position != 0
            if self.is_active:
                self.price_memory = []
                self.group_features_memory = []
        
        def deactivate(self):
            """停用预测系统"""
            self.is_active = False
            self.price_memory = []
            self.group_features_memory = []
        
        def predict_next_prices(self, current_price, hull_prices, group_features):
            """基于群论特征预测未来价格"""
            if not self.is_active:
                return 0.0, 0.0
            
            # 更新记忆
            self.price_memory.append(current_price)
            self.group_features_memory.append(group_features)
            
            # 限制记忆长度
            if len(self.price_memory) > 20:
                self.price_memory = self.price_memory[-20:]
                self.group_features_memory = self.group_features_memory[-20:]
            
            if len(self.price_memory) < 5:
                return current_price, 0.0
            
            # 群论增强的价格预测
            predicted_price = self._group_theory_price_prediction(
                self.price_memory, self.group_features_memory
            )
            
            # 计算预测置信度
            confidence = self._calculate_prediction_confidence(
                self.price_memory, predicted_price
            )
            
            return predicted_price, confidence
        
        def _group_theory_price_prediction(self, prices, group_features):
            """基于群论特征的价格预测"""
            if len(prices) < 3:
                return prices[-1]
            
            # 提取最近的群论特征
            recent_features = group_features[-3:]
            recent_prices = prices[-3:]
            
            # 群论对称性预测
            symmetry_prediction = self._symmetry_based_prediction(recent_prices)
            
            # 群不变量预测
            invariant_prediction = self._invariant_based_prediction(recent_prices, recent_features)
            
            # 李群变换预测
            lie_group_prediction = self._lie_group_prediction(recent_prices)
            
            # 置换群模式预测
            permutation_prediction = self._permutation_pattern_prediction(recent_prices)
            
            # 加权融合预测结果
            weights = [0.3, 0.3, 0.2, 0.2]
            predictions = [
                symmetry_prediction,
                invariant_prediction,
                lie_group_prediction,
                permutation_prediction
            ]
            
            # 计算加权平均预测
            weighted_prediction = sum(w * p for w, p in zip(weights, predictions))
            
            return weighted_prediction
        
        def _symmetry_based_prediction(self, prices):
            """基于对称性的价格预测"""
            if len(prices) < 3:
                return prices[-1]
            
            # 计算价格变化的对称性模式
            changes = [prices[i+1] - prices[i] for i in range(len(prices)-1)]
            
            if len(changes) >= 2:
                # 假设对称性延续
                last_change = changes[-1]
                symmetry_factor = np.mean(changes) / (np.std(changes) + 1e-6)
                
                # 基于对称性预测下一个变化
                predicted_change = last_change * (1 + symmetry_factor * 0.1)
                return prices[-1] + predicted_change
            
            return prices[-1]
        
        def _invariant_based_prediction(self, prices, features):
            """基于群不变量的价格预测"""
            if len(prices) < 3 or len(features) < 3:
                return prices[-1]
            
            # 提取不变量特征
            invariant_values = [f.get('invariant_factor', 0) for f in features]
            
            if len(invariant_values) >= 2:
                # 基于不变量变化趋势预测
                invariant_trend = invariant_values[-1] - invariant_values[-2]
                price_sensitivity = 0.01  # 价格对不变量的敏感度
                
                predicted_price = prices[-1] + invariant_trend * price_sensitivity * prices[-1]
                return predicted_price
            
            return prices[-1]
        
        def _lie_group_prediction(self, prices):
            """基于李群变换的价格预测"""
            if len(prices) < 3:
                return prices[-1]
            
            # 计算对数收益率（李代数元素）
            log_returns = []
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    log_return = math.log(prices[i] / prices[i-1])
                    log_returns.append(log_return)
            
            if len(log_returns) >= 2:
                # 李群指数映射预测
                recent_return = log_returns[-1]
                return_trend = np.mean(log_returns[-2:])
                
                # 预测下一个对数收益率
                predicted_log_return = recent_return + return_trend * 0.1
                
                # 转换回价格
                predicted_price = prices[-1] * math.exp(predicted_log_return)
                return predicted_price
            
            return prices[-1]
        
        def _permutation_pattern_prediction(self, prices):
            """基于置换群模式的价格预测"""
            if len(prices) < 4:
                return prices[-1]
            
            # 计算价格排名模式
            recent_prices = prices[-4:]
            sorted_indices = np.argsort(recent_prices)
            ranks = np.empty_like(sorted_indices)
            ranks[sorted_indices] = np.arange(len(recent_prices))
            
            # 分析排名模式的周期性
            if len(ranks) >= 3:
                # 简单的模式延续预测
                rank_changes = [ranks[i+1] - ranks[i] for i in range(len(ranks)-1)]
                if rank_changes:
                    avg_rank_change = np.mean(rank_changes)
                    
                    # 基于排名变化预测价格变化
                    price_changes = [prices[i+1] - prices[i] for i in range(len(prices)-1)]
                    if price_changes:
                        avg_price_change = np.mean(price_changes)
                        predicted_change = avg_price_change * (1 + avg_rank_change * 0.05)
                        return prices[-1] + predicted_change
            
            return prices[-1]
        
        def _calculate_prediction_confidence(self, prices, predicted_price):
            """计算预测置信度"""
            if len(prices) < 3:
                return 0.0
            
            # 基于历史预测准确性计算置信度
            recent_prices = prices[-3:]
            price_volatility = np.std(recent_prices) / np.mean(recent_prices)
            
            # 预测偏差
            prediction_deviation = abs(predicted_price - prices[-1]) / prices[-1]
            
            # 置信度计算
            volatility_factor = 1.0 / (1.0 + price_volatility * 10)
            deviation_factor = 1.0 / (1.0 + prediction_deviation * 20)
            
            confidence = volatility_factor * deviation_factor
            return min(1.0, max(0.0, confidence))
    
    class AdaptiveRiskController:
        """自适应风险控制系统"""
        
        def __init__(self):
            self.volatility_window = 20
            self.risk_memory = []
            self.performance_memory = []
            
        def calculate_adaptive_levels(self, current_price, entry_price, position_direction, 
                                    market_volatility, prediction_confidence, hull_trend, stc_trend):
            """计算自适应止盈止损水平"""
            
            # 基础风险参数
            base_risk_ratio = 0.02  # 基础风险比例
            base_profit_ratio = 0.04  # 基础盈利比例
            
            # 市场波动率调整
            volatility_adjustment = min(2.0, max(0.5, market_volatility * 50))
            
            # 预测置信度调整
            confidence_adjustment = 0.5 + prediction_confidence * 0.5
            
            # 趋势一致性调整
            trend_consistency = self._calculate_trend_consistency(hull_trend, stc_trend)
            trend_adjustment = 0.7 + trend_consistency * 0.6
            
            # 计算自适应止损水平
            adaptive_stop_ratio = base_risk_ratio * volatility_adjustment / confidence_adjustment
            adaptive_stop_ratio = min(0.05, max(0.01, adaptive_stop_ratio))  # 限制在1%-5%
            
            # 计算自适应止盈水平
            adaptive_profit_ratio = base_profit_ratio * trend_adjustment * confidence_adjustment
            adaptive_profit_ratio = min(0.10, max(0.02, adaptive_profit_ratio))  # 限制在2%-10%
            
            # 根据持仓方向计算具体价格
            if position_direction > 0:  # 多头
                stop_loss_price = entry_price * (1 - adaptive_stop_ratio)
                take_profit_price = entry_price * (1 + adaptive_profit_ratio)
            else:  # 空头
                stop_loss_price = entry_price * (1 + adaptive_stop_ratio)
                take_profit_price = entry_price * (1 - adaptive_profit_ratio)
            
            # 计算当前风险水平
            current_risk = abs(current_price - entry_price) / entry_price
            risk_level = current_risk / adaptive_stop_ratio
            
            return {
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'risk_level': risk_level,
                'adaptive_stop_ratio': adaptive_stop_ratio,
                'adaptive_profit_ratio': adaptive_profit_ratio
            }
        
        def _calculate_trend_consistency(self, hull_trend, stc_trend):
            """计算趋势一致性"""
            if hull_trend == stc_trend:
                if hull_trend in ["BULLISH", "BEARISH"]:
                    return 1.0  # 强趋势一致
                else:
                    return 0.5  # 中性一致
            else:
                return 0.0  # 趋势不一致
        
        def should_adjust_levels(self, current_risk_level, market_change_rate):
            """判断是否需要调整止盈止损水平"""
            # 风险水平过高时调整
            if current_risk_level > 0.8:
                return True
            
            # 市场快速变化时调整
            if abs(market_change_rate) > 0.02:
                return True
            
            return False
        
        def calculate_position_size_adjustment(self, prediction_confidence, market_volatility):
            """计算仓位大小调整因子"""
            # 基于预测置信度和市场波动率调整仓位
            confidence_factor = 0.5 + prediction_confidence * 0.5
            volatility_factor = 1.0 / (1.0 + market_volatility * 10)
            
            adjustment_factor = confidence_factor * volatility_factor
            return min(1.0, max(0.3, adjustment_factor))  # 限制在30%-100%    

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            "HULL": self.state_map.hull_value,
            "STC": self.state_map.stc_value,
        }
    
    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标（智能化增强版）"""
        return {
            "STC_VALUE": self.state_map.stc_value,
            "STC_SIGNAL": self.state_map.stc_signal,
            "FUZZY_SIGNAL": self.state_map.fuzzy_signal,
            "PREDICTION_CONFIDENCE": self.state_map.prediction_confidence,
            "RISK_LEVEL": self.state_map.risk_level,
            "ADAPTIVE_STOP": self.state_map.adaptive_stop_loss,
            "ADAPTIVE_PROFIT": self.state_map.adaptive_take_profit,
        }
    
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)
    
    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None
    
    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交回调"""
        super().on_trade(trade, log)
        self.order_id = None
    
    def on_start(self):
        """策略启动"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        
        super().on_start()
        
        # 初始化信号
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        
        # 清空数据存储
        self.hull_prices = []
        self.hull_wma_half = []
        self.hull_wma_full = []
        self.hull_values = []
        self.stc_prices = []
        self.stc_macd_values = []
        self.stc_stoch1_values = []
        self.stc_stoch2_values = []
        self.group_cache = {}
        
        # 重置智能化系统
        self.prediction_system.deactivate()
        self.prediction_history = []
        
        self.update_status_bar()
    
    def on_stop(self):
        """策略停止"""
        super().on_stop()
    
    def callback(self, kline: KLineData) -> None:
        """K线回调"""
        # 计算指标
        self.calc_indicator(kline)
        
        # 计算信号
        self.calc_signal(kline)
        
        # 信号执行
        self.exec_signal()
        
        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        if self.trading:
            self.update_status_bar()
    
    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        self.calc_indicator(kline)
        
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        self.update_status_bar()
    
    def calc_indicator(self, kline: KLineData) -> None:
        """计算技术指标"""
        # 更新价格数据
        self.hull_prices.append(kline.close)
        self.stc_prices.append(kline.close)
        
        # 限制历史数据长度
        max_length = max(self.params_map.hull_period, self.params_map.stc_slow) + 50
        if len(self.hull_prices) > max_length:
            self.hull_prices = self.hull_prices[-max_length:]
        if len(self.stc_prices) > max_length:
            self.stc_prices = self.stc_prices[-max_length:]
        
        # 计算HULL移动平均线
        self._calc_hull_ma()
        
        # 计算STC指标
        self._calc_stc()
        
        # 智能化预测和风险控制（仅在持仓时激活）
        self._update_intelligent_systems(kline)
    
    def _calc_hull_ma(self) -> None:
        """计算Hull移动平均线 - 完整Python实现"""
        if len(self.hull_prices) < self.params_map.hull_period:
            return
        
        period = self.params_map.hull_period
        half_period = max(1, period // 2)
        sqrt_period = max(1, int(math.sqrt(period)))
        
        # 计算WMA(price, period/2)
        wma_half = self._weighted_moving_average(self.hull_prices, half_period)
        self.hull_wma_half.append(wma_half)
        
        # 计算WMA(price, period)
        wma_full = self._weighted_moving_average(self.hull_prices, period)
        self.hull_wma_full.append(wma_full)
        
        # 计算Hull MA: WMA(2*WMA(n/2) - WMA(n), sqrt(n))
        hull_raw = 2 * wma_half - wma_full
        self.hull_values.append(hull_raw)
        
        # 限制数组长度
        if len(self.hull_values) > sqrt_period + 10:
            self.hull_values = self.hull_values[-(sqrt_period + 10):]
        
        if len(self.hull_values) >= sqrt_period:
            # 计算最终Hull MA值
            self.state_map.hull_prev = self.state_map.hull_value
            self.state_map.hull_value = self._weighted_moving_average(self.hull_values, sqrt_period)
            
            # 更新趋势
            if self.state_map.hull_value > self.state_map.hull_prev:
                self.state_map.hull_trend = "BULLISH"
            elif self.state_map.hull_value < self.state_map.hull_prev:
                self.state_map.hull_trend = "BEARISH"
            else:
                self.state_map.hull_trend = "NEUTRAL"
    
    def _weighted_moving_average(self, data: List[float], period: int) -> float:
        """计算加权移动平均"""
        if len(data) < period:
            return sum(data) / len(data) if data else 0.0
        
        # 使用最近period个数据点
        recent_data = data[-period:]
        weights = np.arange(1, period + 1)
        values = np.array(recent_data)
        
        return np.sum(weights * values) / np.sum(weights)
    
    def _calc_stc(self) -> None:
        """计算Schaff趋势周期指标 - 完整Python实现"""
        if len(self.stc_prices) < self.params_map.stc_slow:
            return
        
        # 计算MACD
        fast_ema = self._exponential_moving_average(self.stc_prices, self.params_map.stc_fast)
        slow_ema = self._exponential_moving_average(self.stc_prices, self.params_map.stc_slow)
        macd = fast_ema - slow_ema
        self.stc_macd_values.append(macd)
        
        # 限制数组长度
        if len(self.stc_macd_values) > self.params_map.stc_cycle + 10:
            self.stc_macd_values = self.stc_macd_values[-(self.params_map.stc_cycle + 10):]
        
        if len(self.stc_macd_values) < self.params_map.stc_cycle:
            return
        
        # 第一次随机化
        stoch1 = self._stochastic_oscillator(self.stc_macd_values, self.params_map.stc_cycle)
        self.stc_stoch1_values.append(stoch1)
        
        # 限制数组长度
        if len(self.stc_stoch1_values) > self.params_map.stc_cycle + 10:
            self.stc_stoch1_values = self.stc_stoch1_values[-(self.params_map.stc_cycle + 10):]
        
        if len(self.stc_stoch1_values) < self.params_map.stc_cycle:
            return
        
        # 第二次随机化
        stoch2 = self._stochastic_oscillator(self.stc_stoch1_values, self.params_map.stc_cycle)
        self.stc_stoch2_values.append(stoch2)
        
        # 更新STC值
        self.state_map.stc_value = stoch2
        
        # 计算信号线（EMA平滑）
        alpha = 2.0 / (self.params_map.stc_cycle + 1)
        self.stc_signal_line = alpha * self.state_map.stc_value + (1 - alpha) * self.stc_signal_line
        self.state_map.stc_signal = self.stc_signal_line
        
        # 更新趋势
        if self.state_map.stc_value > self.state_map.stc_signal:
            self.state_map.stc_trend = "BULLISH"
        elif self.state_map.stc_value < self.state_map.stc_signal:
            self.state_map.stc_trend = "BEARISH"
        else:
            self.state_map.stc_trend = "NEUTRAL"
    
    def _exponential_moving_average(self, data: List[float], period: int) -> float:
        """计算指数移动平均"""
        if not data:
            return 0.0
        if len(data) == 1:
            return data[0]
        
        alpha = 2.0 / (period + 1)
        ema = data[0]
        for price in data[1:]:
            ema = alpha * price + (1 - alpha) * ema
        
        return ema
    
    def _stochastic_oscillator(self, values: List[float], period: int) -> float:
        """计算随机振荡器"""
        if len(values) < period:
            return 50.0
        
        recent_values = values[-period:]
        highest = max(recent_values)
        lowest = min(recent_values)
        current = values[-1]
        
        if highest == lowest:
            return 50.0
        
        return 100 * (current - lowest) / (highest - lowest)   
 
    def _update_intelligent_systems(self, kline: KLineData) -> None:
        """更新智能化系统（预测和自适应风险控制）"""
        # 获取当前持仓状态
        position = self.get_position(self.params_map.instrument_id)
        current_position = position.net_position
        
        # 激活/停用预测系统（仅在持仓时激活）
        if current_position != 0:
            self.prediction_system.activate(current_position)
            self.state_map.prediction_active = True
            
            # 计算群论特征
            group_features = self._extract_group_features_for_prediction()
            
            # 实时预测
            predicted_price, prediction_confidence = self.prediction_system.predict_next_prices(
                kline.close, self.hull_prices, [group_features]
            )
            
            # 更新预测状态
            self.state_map.predicted_price = predicted_price
            self.state_map.prediction_confidence = prediction_confidence
            
            # 计算市场波动率
            market_volatility = self._calculate_market_volatility()
            
            # 自适应风险控制
            if hasattr(position, 'avg_price') and position.avg_price > 0:
                risk_levels = self.adaptive_risk_controller.calculate_adaptive_levels(
                    current_price=kline.close,
                    entry_price=position.avg_price,
                    position_direction=current_position,
                    market_volatility=market_volatility,
                    prediction_confidence=prediction_confidence,
                    hull_trend=self.state_map.hull_trend,
                    stc_trend=self.state_map.stc_trend
                )
                
                # 更新自适应风险控制状态
                self.state_map.adaptive_stop_loss = risk_levels['stop_loss_price']
                self.state_map.adaptive_take_profit = risk_levels['take_profit_price']
                self.state_map.risk_level = risk_levels['risk_level']
        else:
            # 无持仓时停用预测系统
            self.prediction_system.deactivate()
            self.state_map.prediction_active = False
            self.state_map.predicted_price = 0
            self.state_map.prediction_confidence = 0
            self.state_map.adaptive_stop_loss = 0
            self.state_map.adaptive_take_profit = 0
            self.state_map.risk_level = 0
    
    def _extract_group_features_for_prediction(self) -> Dict:
        """为预测系统提取群论特征"""
        return {
            'symmetry_factor': self._calculate_price_symmetry(),
            'invariant_factor': self._calculate_group_invariants(),
            'lie_group_factor': self._apply_lie_group_transformation(0.0),
            'permutation_factor': self._analyze_permutation_patterns()
        }
    
    def _calculate_market_volatility(self) -> float:
        """计算市场波动率"""
        if len(self.hull_prices) < 10:
            return 0.02  # 默认波动率
        
        recent_prices = np.array(self.hull_prices[-10:])
        returns = np.diff(recent_prices) / recent_prices[:-1]
        volatility = np.std(returns) if len(returns) > 1 else 0.02
        
        return volatility
    
    def _apply_group_theory_enhancement(self, signal_strength: float, confidence: float) -> Tuple[float, float]:
        """应用群论数学增强 - 内嵌群论运算"""
        
        # 群论对称性分析
        symmetry_factor = self._calculate_price_symmetry()
        
        # 群不变量计算
        invariant_factor = self._calculate_group_invariants()
        
        # 李群变换增强
        lie_group_factor = self._apply_lie_group_transformation(signal_strength)
        
        # 置换群模式识别
        permutation_factor = self._analyze_permutation_patterns()
        
        # 综合群论增强因子
        group_enhancement = (
            0.3 * symmetry_factor + 
            0.3 * invariant_factor + 
            0.2 * lie_group_factor + 
            0.2 * permutation_factor
        )
        
        # 应用群论增强
        enhanced_signal = signal_strength * (1 + group_enhancement * 0.2)
        enhanced_confidence = confidence * (1 + abs(group_enhancement) * 0.1)
        
        # 限制在合理范围内
        enhanced_signal = max(-1.0, min(1.0, enhanced_signal))
        enhanced_confidence = max(0.0, min(1.0, enhanced_confidence))
        
        return enhanced_signal, enhanced_confidence
    
    def _calculate_price_symmetry(self) -> float:
        """计算价格序列的对称性 - 群论对称性分析"""
        if len(self.hull_prices) < 10:
            return 0.0
        
        # 使用最近的价格数据
        recent_prices = self.hull_prices[-10:]
        
        # 计算价格序列的对称性度量
        center = len(recent_prices) // 2
        left_part = recent_prices[:center]
        right_part = recent_prices[center:][::-1]  # 反转右半部分
        
        if len(left_part) == len(right_part):
            # 计算对称性相关系数
            if len(left_part) > 1:
                correlation = np.corrcoef(left_part, right_part)[0, 1]
                return correlation if not np.isnan(correlation) else 0.0
        
        return 0.0
    
    def _calculate_group_invariants(self) -> float:
        """计算群不变量 - 价格序列的本质特征"""
        if len(self.hull_prices) < 5:
            return 0.0
        
        recent_prices = np.array(self.hull_prices[-5:])
        
        # 计算多项式不变量
        # 一次不变量：平均值
        inv1 = np.mean(recent_prices)
        
        # 二次不变量：方差
        inv2 = np.var(recent_prices)
        
        # 三次不变量：偏度相关
        mean_val = np.mean(recent_prices)
        inv3 = np.mean((recent_prices - mean_val) ** 3)
        
        # 标准化不变量
        price_scale = np.mean(recent_prices) if np.mean(recent_prices) > 0 else 1.0
        normalized_inv2 = inv2 / (price_scale ** 2)
        normalized_inv3 = inv3 / (price_scale ** 3)
        
        # 综合不变量因子
        invariant_factor = np.tanh(normalized_inv2 + abs(normalized_inv3))
        
        return invariant_factor
    
    def _apply_lie_group_transformation(self, signal: float) -> float:
        """应用李群变换 - 连续变换群分析"""
        if len(self.hull_prices) < 3:
            return 0.0
        
        # 计算价格变化的李群表示
        recent_prices = np.array(self.hull_prices[-3:])
        
        # 计算对数变化率（李代数元素）
        if recent_prices[0] > 0 and recent_prices[1] > 0:
            log_change1 = math.log(recent_prices[1] / recent_prices[0])
            log_change2 = math.log(recent_prices[2] / recent_prices[1])
            
            # 李群指数映射近似
            exp_factor = math.exp(log_change1 + log_change2) - 1
            
            # 应用到信号强度
            lie_factor = np.tanh(exp_factor * 10)  # 标准化
            
            return lie_factor
        
        return 0.0
    
    def _analyze_permutation_patterns(self) -> float:
        """分析置换群模式 - 价格序列模式识别"""
        if len(self.hull_prices) < 6:
            return 0.0
        
        recent_prices = self.hull_prices[-6:]
        
        # 计算价格序列的排列模式
        # 将价格转换为排名
        sorted_indices = np.argsort(recent_prices)
        ranks = np.empty_like(sorted_indices)
        ranks[sorted_indices] = np.arange(len(recent_prices))
        
        # 计算置换的循环结构
        cycles = self._find_permutation_cycles(ranks)
        
        # 基于循环结构计算模式强度
        if cycles:
            # 较长的循环表示更强的模式
            max_cycle_length = max(len(cycle) for cycle in cycles)
            pattern_strength = max_cycle_length / len(recent_prices)
            
            return pattern_strength
        
        return 0.0
    
    def _find_permutation_cycles(self, permutation: np.ndarray) -> List[List[int]]:
        """寻找置换的循环结构"""
        n = len(permutation)
        visited = [False] * n
        cycles = []
        
        for i in range(n):
            if not visited[i]:
                cycle = []
                j = i
                while not visited[j]:
                    visited[j] = True
                    cycle.append(j)
                    j = permutation[j]
                
                if len(cycle) > 1:
                    cycles.append(cycle)
        
        return cycles
    
    def _fuzzy_inference_with_group_theory(self, hull_signal: str, stc_signal: str, pattern_signal: str) -> Tuple[float, float, str]:
        """群论增强的模糊推理"""
        
        # 匹配模糊规则
        matched_rules = []
        for rule in self.fuzzy_rules:
            if (rule["hull"] == hull_signal and 
                rule["stc"] == stc_signal and 
                rule["pattern"] == pattern_signal):
                matched_rules.append(rule)
        
        if not matched_rules:
            # 部分匹配
            for rule in self.fuzzy_rules:
                match_count = 0
                if rule["hull"] == hull_signal:
                    match_count += 1
                if rule["stc"] == stc_signal:
                    match_count += 1
                if rule["pattern"] == pattern_signal:
                    match_count += 1
                
                if match_count >= 2:
                    # 降低权重的部分匹配
                    partial_rule = rule.copy()
                    partial_rule["weight"] *= 0.7
                    matched_rules.append(partial_rule)
        
        if matched_rules:
            # 选择权重最高的规则
            best_rule = max(matched_rules, key=lambda x: x["weight"])
            
            # 输出映射
            output_values = {
                "strong_sell": -0.8,
                "sell": -0.3,
                "hold": 0.0,
                "buy": 0.3,
                "strong_buy": 0.8
            }
            
            base_signal = output_values.get(best_rule["output"], 0.0)
            base_confidence = best_rule["weight"]
            
            # 应用群论增强因子
            group_factor = best_rule.get("group_factor", 1.0)
            enhanced_signal = base_signal * group_factor
            
            # 应用群论数学增强
            final_signal, final_confidence = self._apply_group_theory_enhancement(
                enhanced_signal, base_confidence
            )
            
            return final_signal, final_confidence, best_rule["output"]
        
        # 默认输出
        return 0.0, 0.0, "hold"
    
    def calc_signal(self, kline: KLineData):
        """计算交易信号（智能化增强版）"""
        
        # 转换指标状态为模糊输入
        hull_signal = "neutral"
        if self.state_map.hull_trend == "BULLISH":
            hull_signal = "bullish"
        elif self.state_map.hull_trend == "BEARISH":
            hull_signal = "bearish"
        
        stc_signal = "neutral"
        if self.state_map.stc_trend == "BULLISH":
            stc_signal = "bullish"
        elif self.state_map.stc_trend == "BEARISH":
            stc_signal = "bearish"
        
        # 智能化预测信号（仅在持仓时考虑）
        prediction_signal = "neutral"
        if self.state_map.prediction_active and self.state_map.prediction_confidence > 0.6:
            price_change_prediction = (self.state_map.predicted_price - kline.close) / kline.close
            if price_change_prediction > 0.01:
                prediction_signal = "bullish"
            elif price_change_prediction < -0.01:
                prediction_signal = "bearish"
        
        # 群论增强的智能化模糊推理
        fuzzy_signal, fuzzy_confidence, fuzzy_action = self._intelligent_fuzzy_inference_with_group_theory(
            hull_signal, stc_signal, prediction_signal
        )
        
        # 更新状态
        self.state_map.fuzzy_signal = fuzzy_signal
        self.state_map.fuzzy_confidence = fuzzy_confidence
        self.state_map.fuzzy_action = fuzzy_action
        
        # 生成最终交易信号
        self.state_map.final_signal = fuzzy_signal
        self.state_map.signal_confidence = fuzzy_confidence
        
        # 智能化交易行动判断
        self._intelligent_trading_decision(fuzzy_signal, fuzzy_confidence, kline)
        
        # 设置价格
        self.long_price = self.short_price = kline.close
        
        if self.tick:
            self.long_price = self.tick.ask_price1
            self.short_price = self.tick.bid_price1
            
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2
                self.short_price = self.tick.bid_price2
        
        # 根据交易方向调整信号
        if self.params_map.trade_direction == "sell":
            self.buy_signal, self.short_signal = self.short_signal, self.buy_signal
        
        self.sell_signal = self.short_signal
        self.cover_signal = self.buy_signal
    
    def _intelligent_fuzzy_inference_with_group_theory(self, hull_signal: str, stc_signal: str, prediction_signal: str) -> Tuple[float, float, str]:
        """智能化群论增强的模糊推理（包含预测信号）"""
        
        # 扩展的模糊规则（包含预测信号）
        enhanced_rules = []
        for rule in self.fuzzy_rules:
            # 为每个原有规则添加预测信号维度
            for pred_sig in ["bullish", "neutral", "bearish"]:
                enhanced_rule = rule.copy()
                enhanced_rule["prediction"] = pred_sig
                
                # 预测信号一致时增强权重
                if pred_sig == rule["output"].replace("strong_", "").replace("_", ""):
                    enhanced_rule["weight"] *= 1.2
                    enhanced_rule["group_factor"] *= 1.1
                elif pred_sig != "neutral":
                    enhanced_rule["weight"] *= 0.8
                
                enhanced_rules.append(enhanced_rule)
        
        # 匹配增强规则
        matched_rules = []
        for rule in enhanced_rules:
            match_score = 0
            if rule["hull"] == hull_signal:
                match_score += 1
            if rule["stc"] == stc_signal:
                match_score += 1
            if rule.get("prediction", "neutral") == prediction_signal:
                match_score += 1
            
            if match_score >= 2:  # 至少匹配2个条件
                rule["match_score"] = match_score
                matched_rules.append(rule)
        
        if matched_rules:
            # 选择匹配度和权重最高的规则
            best_rule = max(matched_rules, key=lambda x: x["weight"] * x["match_score"])
            
            # 输出映射
            output_values = {
                "strong_sell": -0.8,
                "sell": -0.3,
                "hold": 0.0,
                "buy": 0.3,
                "strong_buy": 0.8
            }
            
            base_signal = output_values.get(best_rule["output"], 0.0)
            base_confidence = best_rule["weight"] * (best_rule["match_score"] / 3.0)
            
            # 应用群论增强因子
            group_factor = best_rule.get("group_factor", 1.0)
            enhanced_signal = base_signal * group_factor
            
            # 应用群论数学增强
            final_signal, final_confidence = self._apply_group_theory_enhancement(
                enhanced_signal, base_confidence
            )
            
            return final_signal, final_confidence, best_rule["output"]
        
        # 默认输出
        return 0.0, 0.0, "hold"
    
    def _intelligent_trading_decision(self, fuzzy_signal: float, fuzzy_confidence: float, kline: KLineData):
        """智能化交易决策"""
        # 基础信号判断
        base_threshold = self.params_map.signal_threshold
        base_confidence_threshold = self.params_map.confidence_threshold
        
        # 智能化阈值调整
        adjusted_threshold = base_threshold
        adjusted_confidence_threshold = base_confidence_threshold
        
        # 根据预测置信度调整阈值
        if self.state_map.prediction_active:
            prediction_factor = 1.0 - self.state_map.prediction_confidence * 0.2
            adjusted_threshold *= prediction_factor
            adjusted_confidence_threshold *= (1.0 - self.state_map.prediction_confidence * 0.1)
        
        # 根据市场波动率调整阈值
        market_volatility = self._calculate_market_volatility()
        volatility_factor = 1.0 + market_volatility * 5
        adjusted_threshold *= volatility_factor
        
        # 判断交易行动
        if fuzzy_confidence > adjusted_confidence_threshold:
            if fuzzy_signal > adjusted_threshold:
                self.state_map.trading_action = "BUY"
                self.buy_signal = True
                self.short_signal = False
            elif fuzzy_signal < -adjusted_threshold:
                self.state_map.trading_action = "SELL"
                self.buy_signal = False
                self.short_signal = True
            else:
                self.state_map.trading_action = "HOLD"
                self.buy_signal = False
                self.short_signal = False
        else:
            self.state_map.trading_action = "WAIT"
            self.buy_signal = False
            self.short_signal = False
    
    def exec_signal(self):
        """执行交易信号（智能化增强版）"""
        self.signal_price = 0
        
        position = self.get_position(self.params_map.instrument_id)
        current_price = self.tick.last_price if self.tick else self.long_price
        
        if self.order_id is not None:
            # 挂单未成交，撤单
            self.cancel_order(self.order_id)
        
        # 智能化自适应止盈止损检查
        if position.net_position != 0:
            should_close = self._check_adaptive_stop_levels(position, current_price)
            if should_close:
                self._execute_adaptive_close(position, current_price)
                return
        
        # 平仓逻辑（信号驱动）
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
        elif position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
        
        # 智能化开仓逻辑
        if self.short_signal:
            self.signal_price = -self.short_price
            
            if self.trading:
                # 智能化仓位大小调整
                adjusted_volume = self._calculate_intelligent_position_size()
                
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=adjusted_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
        elif self.buy_signal:
            self.signal_price = self.long_price
            
            if self.trading:
                # 智能化仓位大小调整
                adjusted_volume = self._calculate_intelligent_position_size()
                
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=adjusted_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
    
    def _check_adaptive_stop_levels(self, position, current_price) -> bool:
        """检查自适应止盈止损水平"""
        if not self.params_map.adaptive_risk_enabled:
            return False
        
        if self.state_map.adaptive_stop_loss == 0 or self.state_map.adaptive_take_profit == 0:
            return False
        
        # 多头持仓检查
        if position.net_position > 0:
            # 止损检查
            if current_price <= self.state_map.adaptive_stop_loss:
                return True
            # 止盈检查
            if current_price >= self.state_map.adaptive_take_profit:
                return True
        
        # 空头持仓检查
        elif position.net_position < 0:
            # 止损检查
            if current_price >= self.state_map.adaptive_stop_loss:
                return True
            # 止盈检查
            if current_price <= self.state_map.adaptive_take_profit:
                return True
        
        return False
    
    def _execute_adaptive_close(self, position, current_price):
        """执行自适应平仓"""
        if position.net_position > 0:
            # 平多仓
            self.signal_price = -current_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=current_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
        elif position.net_position < 0:
            # 平空仓
            self.signal_price = current_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=current_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
    
    def _calculate_intelligent_position_size(self) -> int:
        """计算智能化仓位大小"""
        base_volume = self.params_map.order_volume
        
        # 基于预测置信度和市场波动率调整仓位
        if self.state_map.prediction_active:
            market_volatility = self._calculate_market_volatility()
            adjustment_factor = self.adaptive_risk_controller.calculate_position_size_adjustment(
                self.state_map.prediction_confidence, market_volatility
            )
            
            adjusted_volume = int(base_volume * adjustment_factor)
            return max(1, adjusted_volume)  # 至少1手
        
        return base_volume