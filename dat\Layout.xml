<?xml version="1.0" encoding="GB2312"?>
<!--User Layout File-->
<Layout>
    <PageContainer Width="1920" Height="953" WindowLink="true">
        <Page DockPos="0,0,1920,953" DockParentPos="0,0,0,0" DockHorizontal="1" DockContainer="1" DockChildNo="-1" DockSplitterType="-1"/>
        <Page DockPos="0,0,960,953" DockParentPos="0,0,1920,953" DockHorizontal="1" DockContainer="1" DockChildNo="0" DockSplitterType="1"/>
        <Page DockPos="0,0,131,953" DockParentPos="0,0,960,953" DockHorizontal="0" DockContainer="0" DockChildNo="0" DockSplitterType="1">
            <DockWindow id="2101" SetInfo="{&quot;InvestorID&quot;:&quot;ruobing1128-15116891680&quot;}"/>
        </Page>
        <Page DockPos="131,0,957,953" DockParentPos="0,0,960,953" DockHorizontal="0" DockContainer="1" DockChildNo="1" DockSplitterType="-1"/>
        <Page DockPos="131,0,957,606" DockParentPos="131,0,957,953" DockHorizontal="0" DockContainer="0" DockChildNo="0" DockSplitterType="2">
            <DockWindow id="2102" SetInfo="{&quot;BasePrice&quot;:0,&quot;QuoteTab&quot;:7,&quot;tabManager&quot;:{&quot;optcustomspread&quot;:true,&quot;optfuture&quot;:true,&quot;optindex&quot;:true,&quot;optmain&quot;:true,&quot;optposition&quot;:true,&quot;optspot&quot;:true,&quot;optspread&quot;:true,&quot;optwatch&quot;:true}}
"/>
            <DockWindow id="7001" SetInfo="{&quot;LayoutHeight&quot;:281}
"/>
        </Page>
        <Page DockPos="131,606,957,953" DockParentPos="131,0,957,953" DockHorizontal="1" DockContainer="0" DockChildNo="1" DockSplitterType="-1">
            <DockWindow id="2104" SetInfo="{&quot;NoAlgo&quot;:{&quot;Filter&quot;:[true,true,true,true,true,true,true,true,true],&quot;Switch&quot;:false},&quot;OrderTab&quot;:0,&quot;tabManager&quot;:{&quot;optallorder&quot;:true,&quot;optcancelorder&quot;:true,&quot;optexecorder&quot;:true,&quot;optfilledorder&quot;:true,&quot;optoffsetsetting&quot;:true,&quot;optoptionselfclose&quot;:true,&quot;optremainorder&quot;:true,&quot;opttrade&quot;:true,&quot;opttradesummary&quot;:true}}
"/>
            <DockWindow id="2107" SetInfo="{&quot;InfoAutoExport&quot;:false,&quot;InfoAutoExportPath&quot;:&quot;&quot;,&quot;bAutoSaveByOptionSpread&quot;:false,&quot;bAutoSaveByRolling&quot;:false,&quot;bAutoSaveBySpread&quot;:false,&quot;nTab&quot;:1,&quot;tabManager&quot;:{&quot;optallalgo&quot;:true,&quot;optoptionspread&quot;:true,&quot;optpythongo&quot;:true,&quot;optrolling&quot;:true,&quot;optservertrade&quot;:true,&quot;optsmartorder&quot;:true,&quot;optspread&quot;:true,&quot;optvxtrade&quot;:true}}
"/>
            <DockWindow id="2105" SetInfo="{&quot;AutoExport&quot;:null,&quot;CkbBuy&quot;:true,&quot;CkbFutures&quot;:true,&quot;CkbOption&quot;:true,&quot;CkbSell&quot;:true,&quot;TabPosition&quot;:0}
"/>
        </Page>
        <Page DockPos="960,0,1920,953" DockParentPos="0,0,1920,953" DockHorizontal="0" DockContainer="0" DockChildNo="1" DockSplitterType="-1">
            <DockWindow id="3001" SetInfo="{&quot;BasePrice&quot;:0,&quot;HedgeFlag&quot;:0,&quot;OffsetFlag&quot;:0,&quot;PriceType&quot;:3,&quot;TCondition&quot;:0,&quot;Thickness&quot;:10,&quot;Volume&quot;:1,&quot;exchangeID&quot;:&quot;SHFE&quot;,&quot;isComb&quot;:0,&quot;isFold&quot;:true,&quot;isLink&quot;:1,&quot;kLineMode&quot;:1,&quot;kLineType&quot;:2,&quot;posiType&quot;:0,&quot;stockID&quot;:&quot;au2510&quot;,&quot;techs&quot;:[{&quot;tech&quot;:&quot;VOLUME&quot;},{&quot;tech&quot;:&quot;MACD&quot;},{&quot;tech&quot;:&quot;MA&quot;}]}"/>
            <DockWindow id="3001" SetInfo="{&quot;BasePrice&quot;:0,&quot;HedgeFlag&quot;:0,&quot;OffsetFlag&quot;:0,&quot;PriceType&quot;:3,&quot;TCondition&quot;:0,&quot;Thickness&quot;:10,&quot;Volume&quot;:1,&quot;exchangeID&quot;:&quot;SHFE&quot;,&quot;isComb&quot;:0,&quot;isFold&quot;:true,&quot;isLink&quot;:1,&quot;kLineMode&quot;:1,&quot;kLineType&quot;:2,&quot;posiType&quot;:0,&quot;stockID&quot;:&quot;au2510&quot;,&quot;techs&quot;:[{&quot;tech&quot;:&quot;VOLUME&quot;},{&quot;tech&quot;:&quot;MACD&quot;},{&quot;tech&quot;:&quot;MA&quot;}]}"/>
        </Page>
    </PageContainer>
    <NormalWindowContainer>
        <NormalWindow id="100045" WindowPos="1410,730,1910,1030" IsMin="0"/>
    </NormalWindowContainer>
    <UsedPageCintainerList/>
</Layout>
