from typing import Literal
import logging
import numpy as np

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型 - 终极优化版本
    
    基于活跃社区最佳实践，融合多种技术指标
    """
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="D1", title="K线周期")
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    
    # 多重EMA系统 - 社区最佳实践
    ema_fast: int = Field(default=12, title="快速EMA周期")
    ema_medium: int = Field(default=26, title="中速EMA周期") 
    ema_slow: int = Field(default=50, title="慢速EMA周期")
    
    # MACD参数 - 经典动量指标
    macd_fast: int = Field(default=12, title="MACD快线")
    macd_slow: int = Field(default=26, title="MACD慢线")
    macd_signal: int = Field(default=9, title="MACD信号线")
    
    # RSI参数 - 超买超卖指标
    rsi_period: int = Field(default=14, title="RSI周期")
    rsi_overbought: float = Field(default=70, title="RSI超买线")
    rsi_oversold: float = Field(default=30, title="RSI超卖线")
    
    # 布林带参数 - 波动性指标
    bb_period: int = Field(default=20, title="布林带周期")
    bb_std: float = Field(default=2.0, title="布林带标准差倍数")
    
    # 成交量确认
    volume_ma_period: int = Field(default=20, title="成交量均线周期")
    volume_threshold: float = Field(default=1.2, title="成交量放大倍数")
    
    # ADX趋势强度
    adx_period: int = Field(default=14, title="ADX周期")
    adx_threshold: float = Field(default=25, title="ADX趋势阈值")
    
    # 信号融合权重
    ema_weight: float = Field(default=0.3, title="EMA信号权重")
    macd_weight: float = Field(default=0.25, title="MACD信号权重")
    rsi_weight: float = Field(default=0.2, title="RSI信号权重")
    bb_weight: float = Field(default=0.15, title="布林带信号权重")
    volume_weight: float = Field(default=0.1, title="成交量信号权重")
    
    # 信号阈值
    signal_threshold: float = Field(default=0.6, title="综合信号阈值")
    
    # ATR止损
    atr_period: int = Field(default=14, title="ATR周期")
    stop_atr_multiplier: float = Field(default=2.0, title="止损ATR倍数")


class State(BaseState):
    """状态映射模型 - 终极优化版本"""
    # 多重EMA系统
    ema_fast: float = Field(default=0, title="快速EMA")
    ema_medium: float = Field(default=0, title="中速EMA")
    ema_slow: float = Field(default=0, title="慢速EMA")
    
    # MACD指标
    macd_line: float = Field(default=0, title="MACD线")
    macd_signal: float = Field(default=0, title="MACD信号线")
    macd_histogram: float = Field(default=0, title="MACD柱状图")
    
    # RSI指标
    rsi: float = Field(default=50, title="RSI值")
    
    # 布林带指标
    bb_upper: float = Field(default=0, title="布林带上轨")
    bb_middle: float = Field(default=0, title="布林带中轨")
    bb_lower: float = Field(default=0, title="布林带下轨")
    bb_percent: float = Field(default=0, title="布林带百分比")
    
    # 成交量指标
    volume_ma: float = Field(default=0, title="成交量均线")
    volume_ratio: float = Field(default=1, title="成交量比率")
    
    # ADX趋势强度
    adx: float = Field(default=0, title="ADX值")
    di_plus: float = Field(default=0, title="DI+")
    di_minus: float = Field(default=0, title="DI-")
    
    # ATR
    atr: float = Field(default=0, title="ATR值")
    
    # 综合信号
    bull_signal_strength: float = Field(default=0, title="多头信号强度")
    bear_signal_strength: float = Field(default=0, title="空头信号强度")
    
    # 订单管理
    pending_order: bool = Field(default=False, title="是否有挂单")
    kline_count: int = Field(default=0, title="K线计数")
    
    # 价格历史数据
    high_prices: list = Field(default_factory=list, title="最高价历史")
    low_prices: list = Field(default_factory=list, title="最低价历史")
    close_prices: list = Field(default_factory=list, title="收盘价历史")
    volume_data: list = Field(default_factory=list, title="成交量历史")


class VegasTunnelStrategy(BaseStrategy):
    """维加斯隧道交易法策略 - 终极优化版本
    
    基于活跃社区最佳实践，融合多种技术指标：
    1. 多重EMA系统 (12/26/50)
    2. MACD动量确认
    3. RSI超买超卖过滤
    4. 布林带波动性分析
    5. 成交量确认
    6. ADX趋势强度
    7. 综合信号评分系统
    """
    
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()

        # 信号标志
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False

        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 初始化交易价格
        self.long_price = 0
        self.short_price = 0
        
        # 设置日志
        self.logger = logging.getLogger(f"{self.__class__.__name__}")

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            f"EMA{self.params_map.ema_fast}": self.state_map.ema_fast,
            f"EMA{self.params_map.ema_medium}": self.state_map.ema_medium,
            f"EMA{self.params_map.ema_slow}": self.state_map.ema_slow,
            "BB_Upper": self.state_map.bb_upper,
            "BB_Middle": self.state_map.bb_middle,
            "BB_Lower": self.state_map.bb_lower
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "MACD": self.state_map.macd_line,
            "MACD_Signal": self.state_map.macd_signal,
            "MACD_Hist": self.state_map.macd_histogram,
            "RSI": self.state_map.rsi,
            "ADX": self.state_map.adx,
            "Bull_Strength": self.state_map.bull_signal_strength,
            "Bear_Strength": self.state_map.bear_signal_strength
        }
   
    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None
        self.state_map.pending_order = False
        self.logger.info(f"订单已撤销: {order.order_id}")

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交推送回调"""
        super().on_trade(trade, log)
        self.order_id = None
        self.state_map.pending_order = False
        
        action = "开仓" if trade.offset == "open" else "平仓"
        direction = "买入" if trade.direction == "buy" else "卖出"
        self.logger.info(f"{action}{direction}: 价格={trade.price}, 数量={trade.volume}")
        
        if trade.offset == "close":
            self._check_open_signal_after_close()

    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()
        self._reset_state()
        
        self.logger.info(f"终极优化策略启动: {self.params_map.instrument_id}, "
                        f"多指标融合系统, 方向={self.params_map.trade_direction}")
        self.update_status_bar()

    def on_stop(self):
        super().on_stop()
        self.logger.info("策略停止")

    def _reset_state(self):
        """重置策略状态"""
        self.signal_price = 0
        self.long_price = 0
        self.short_price = 0

        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

        self.tick = None
        self.state_map.pending_order = False
        self.state_map.kline_count = 0
        
        # 重置历史数据
        self.state_map.high_prices = []
        self.state_map.low_prices = []
        self.state_map.close_prices = []
        self.state_map.volume_data = []

    def callback(self, kline: KLineData) -> None:
        """K线回调 - 终极优化版"""
        try:
            # 计算所有技术指标
            self.calc_all_indicators(kline)

            # 如果有挂单，跳过信号处理
            if self.state_map.pending_order:
                self.logger.debug("存在挂单，跳过信号处理")
                self._update_display(kline)
                return

            # 计算综合信号
            self.calc_composite_signals(kline)

            # 信号执行
            self.exec_signal_enhanced()

            # 更新显示
            self._update_display(kline)

        except Exception as e:
            self.logger.error(f"K线回调异常: {e}")

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        try:
            self.calc_all_indicators(kline, is_realtime=True)
            self._update_display(kline)
        except Exception as e:
            self.logger.error(f"实时回调异常: {e}")

    def calc_all_indicators(self, kline: KLineData, is_realtime: bool = False) -> None:
        """计算所有技术指标"""
        close_price = kline.close
        high_price = kline.high
        low_price = kline.low
        volume = getattr(kline, 'volume', 0)
        
        if not is_realtime:
            self.state_map.kline_count += 1
            
            # 更新历史数据
            self.state_map.high_prices.append(high_price)
            self.state_map.low_prices.append(low_price)
            self.state_map.close_prices.append(close_price)
            self.state_map.volume_data.append(volume)
            
            # 保持历史长度
            max_history = max(self.params_map.ema_slow, self.params_map.bb_period, 
                            self.params_map.adx_period, self.params_map.volume_ma_period) + 20
            if len(self.state_map.close_prices) > max_history:
                self.state_map.high_prices = self.state_map.high_prices[-max_history:]
                self.state_map.low_prices = self.state_map.low_prices[-max_history:]
                self.state_map.close_prices = self.state_map.close_prices[-max_history:]
                self.state_map.volume_data = self.state_map.volume_data[-max_history:]
        
        if not is_realtime and len(self.state_map.close_prices) >= 2:
            # 计算各种技术指标
            self._calc_ema_system()
            self._calc_macd()
            self._calc_rsi()
            self._calc_bollinger_bands()
            self._calc_volume_indicators()
            self._calc_adx()
            self._calc_atr()
        
    def _calc_ema_system(self):
        """计算多重EMA系统"""
        closes = np.array(self.state_map.close_prices)
        
        # 计算三条EMA
        self.state_map.ema_fast = self._ema(closes, self.params_map.ema_fast)
        self.state_map.ema_medium = self._ema(closes, self.params_map.ema_medium)
        self.state_map.ema_slow = self._ema(closes, self.params_map.ema_slow)

    def _calc_macd(self):
        """计算MACD指标"""
        closes = np.array(self.state_map.close_prices)
        
        # 计算MACD
        ema_fast = self._ema(closes, self.params_map.macd_fast)
        ema_slow = self._ema(closes, self.params_map.macd_slow)
        
        self.state_map.macd_line = ema_fast - ema_slow
        
        # 计算信号线（MACD的EMA）
        if hasattr(self, '_macd_history'):
            self._macd_history.append(self.state_map.macd_line)
            if len(self._macd_history) > 50:
                self._macd_history = self._macd_history[-50:]
        else:
            self._macd_history = [self.state_map.macd_line]
        
        if len(self._macd_history) >= self.params_map.macd_signal:
            self.state_map.macd_signal = self._ema(np.array(self._macd_history), self.params_map.macd_signal)
        else:
            self.state_map.macd_signal = self.state_map.macd_line
            
        self.state_map.macd_histogram = self.state_map.macd_line - self.state_map.macd_signal

    def _calc_rsi(self):
        """计算RSI指标"""
        closes = np.array(self.state_map.close_prices)
        
        if len(closes) < self.params_map.rsi_period + 1:
            self.state_map.rsi = 50
            return
            
        # 计算价格变化
        deltas = np.diff(closes)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        # 计算平均收益和损失
        avg_gain = np.mean(gains[-self.params_map.rsi_period:])
        avg_loss = np.mean(losses[-self.params_map.rsi_period:])
        
        if avg_loss == 0:
            self.state_map.rsi = 100
        else:
            rs = avg_gain / avg_loss
            self.state_map.rsi = 100 - (100 / (1 + rs))

    def _calc_bollinger_bands(self):
        """计算布林带指标"""
        closes = np.array(self.state_map.close_prices)
        
        if len(closes) < self.params_map.bb_period:
            self.state_map.bb_middle = closes[-1]
            self.state_map.bb_upper = closes[-1]
            self.state_map.bb_lower = closes[-1]
            self.state_map.bb_percent = 0.5
            return
        
        # 计算中轨（SMA）
        self.state_map.bb_middle = np.mean(closes[-self.params_map.bb_period:])
        
        # 计算标准差
        std = np.std(closes[-self.params_map.bb_period:])
        
        # 计算上下轨
        self.state_map.bb_upper = self.state_map.bb_middle + (self.params_map.bb_std * std)
        self.state_map.bb_lower = self.state_map.bb_middle - (self.params_map.bb_std * std)
        
        # 计算布林带百分比
        if self.state_map.bb_upper != self.state_map.bb_lower:
            self.state_map.bb_percent = (closes[-1] - self.state_map.bb_lower) / (self.state_map.bb_upper - self.state_map.bb_lower)
        else:
            self.state_map.bb_percent = 0.5

    def _calc_volume_indicators(self):
        """计算成交量指标"""
        volumes = np.array(self.state_map.volume_data)
        
        if len(volumes) < self.params_map.volume_ma_period:
            self.state_map.volume_ma = volumes[-1] if len(volumes) > 0 else 0
            self.state_map.volume_ratio = 1
            return
        
        # 计算成交量均线
        self.state_map.volume_ma = np.mean(volumes[-self.params_map.volume_ma_period:])
        
        # 计算成交量比率
        if self.state_map.volume_ma > 0:
            self.state_map.volume_ratio = volumes[-1] / self.state_map.volume_ma
        else:
            self.state_map.volume_ratio = 1

    def _calc_adx(self):
        """计算ADX趋势强度指标"""
        if len(self.state_map.close_prices) < self.params_map.adx_period + 1:
            self.state_map.adx = 0
            self.state_map.di_plus = 0
            self.state_map.di_minus = 0
            return
        
        highs = np.array(self.state_map.high_prices[-self.params_map.adx_period-1:])
        lows = np.array(self.state_map.low_prices[-self.params_map.adx_period-1:])
        closes = np.array(self.state_map.close_prices[-self.params_map.adx_period-1:])
        
        # 计算True Range
        tr1 = highs[1:] - lows[1:]
        tr2 = np.abs(highs[1:] - closes[:-1])
        tr3 = np.abs(lows[1:] - closes[:-1])
        tr = np.maximum(tr1, np.maximum(tr2, tr3))
        
        # 计算方向移动
        dm_plus = np.where((highs[1:] - highs[:-1]) > (lows[:-1] - lows[1:]), 
                          np.maximum(highs[1:] - highs[:-1], 0), 0)
        dm_minus = np.where((lows[:-1] - lows[1:]) > (highs[1:] - highs[:-1]), 
                           np.maximum(lows[:-1] - lows[1:], 0), 0)
        
        # 计算平滑的TR和DM
        atr = np.mean(tr)
        adm_plus = np.mean(dm_plus)
        adm_minus = np.mean(dm_minus)
        
        # 计算DI
        if atr > 0:
            self.state_map.di_plus = 100 * adm_plus / atr
            self.state_map.di_minus = 100 * adm_minus / atr
            
            # 计算ADX
            dx = 100 * abs(self.state_map.di_plus - self.state_map.di_minus) / (self.state_map.di_plus + self.state_map.di_minus + 1e-10)
            self.state_map.adx = dx
        else:
            self.state_map.di_plus = 0
            self.state_map.di_minus = 0
            self.state_map.adx = 0

    def _calc_atr(self):
        """计算ATR指标"""
        if len(self.state_map.close_prices) < 2:
            self.state_map.atr = 0
            return
            
        period = min(self.params_map.atr_period, len(self.state_map.close_prices) - 1)
        if period < 1:
            return
            
        true_ranges = []
        for i in range(len(self.state_map.close_prices) - period, len(self.state_map.close_prices)):
            if i == 0:
                continue
                
            high = self.state_map.high_prices[i]
            low = self.state_map.low_prices[i]
            prev_close = self.state_map.close_prices[i-1]
            
            tr1 = high - low
            tr2 = abs(high - prev_close)
            tr3 = abs(low - prev_close)
            
            true_ranges.append(max(tr1, tr2, tr3))
        
        if true_ranges:
            self.state_map.atr = sum(true_ranges) / len(true_ranges)

    def _ema(self, data, period):
        """计算EMA"""
        if len(data) < period:
            return data[-1] if len(data) > 0 else 0
        
        alpha = 2.0 / (period + 1)
        ema = data[0]
        
        for price in data[1:]:
            ema = alpha * price + (1 - alpha) * ema
            
        return ema 
    def calc_composite_signals(self, kline: KLineData):
        """计算综合信号 - 多指标融合"""
        
        self._reset_signals()
        
        try:
            position = self.get_position(self.params_map.instrument_id)
            current_position = position.net_position
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            return
        
        if self.state_map.kline_count < max(self.params_map.ema_slow, self.params_map.bb_period):
            return
        
        # 1. EMA信号评分
        ema_score = self._calc_ema_signal_score()
        
        # 2. MACD信号评分
        macd_score = self._calc_macd_signal_score()
        
        # 3. RSI信号评分
        rsi_score = self._calc_rsi_signal_score()
        
        # 4. 布林带信号评分
        bb_score = self._calc_bb_signal_score()
        
        # 5. 成交量信号评分
        volume_score = self._calc_volume_signal_score()
        
        # 计算综合信号强度
        self.state_map.bull_signal_strength = (
            ema_score * self.params_map.ema_weight +
            macd_score * self.params_map.macd_weight +
            rsi_score * self.params_map.rsi_weight +
            bb_score * self.params_map.bb_weight +
            volume_score * self.params_map.volume_weight
        )
        
        self.state_map.bear_signal_strength = -self.state_map.bull_signal_strength
        
        # ADX趋势强度过滤
        trend_filter = self.state_map.adx >= self.params_map.adx_threshold
        
        # 生成最终信号
        self._generate_final_signals(current_position, trend_filter)
        
        # 更新交易价格
        self._update_trade_prices(kline)
        
        # 记录信号详情
        if abs(self.state_map.bull_signal_strength) >= self.params_map.signal_threshold:
            signal_type = "多头" if self.state_map.bull_signal_strength > 0 else "空头"
            self.logger.info(f"{signal_type}综合信号: 强度={abs(self.state_map.bull_signal_strength):.3f}, "
                           f"EMA={ema_score:.2f}, MACD={macd_score:.2f}, RSI={rsi_score:.2f}, "
                           f"BB={bb_score:.2f}, VOL={volume_score:.2f}, ADX={self.state_map.adx:.1f}")

    def _calc_ema_signal_score(self) -> float:
        """计算EMA信号评分 (-1到1)"""
        # 多重EMA排列
        if self.state_map.ema_fast > self.state_map.ema_medium > self.state_map.ema_slow:
            # 多头排列
            return 1.0
        elif self.state_map.ema_fast < self.state_map.ema_medium < self.state_map.ema_slow:
            # 空头排列
            return -1.0
        else:
            # 混乱排列，根据快线与慢线关系给分
            if self.state_map.ema_fast > self.state_map.ema_slow:
                return 0.5
            elif self.state_map.ema_fast < self.state_map.ema_slow:
                return -0.5
            else:
                return 0.0

    def _calc_macd_signal_score(self) -> float:
        """计算MACD信号评分 (-1到1)"""
        score = 0.0
        
        # MACD线位置
        if self.state_map.macd_line > 0:
            score += 0.4
        elif self.state_map.macd_line < 0:
            score -= 0.4
        
        # MACD与信号线关系
        if self.state_map.macd_line > self.state_map.macd_signal:
            score += 0.4
        elif self.state_map.macd_line < self.state_map.macd_signal:
            score -= 0.4
        
        # 柱状图趋势
        if self.state_map.macd_histogram > 0:
            score += 0.2
        elif self.state_map.macd_histogram < 0:
            score -= 0.2
        
        return max(-1.0, min(1.0, score))

    def _calc_rsi_signal_score(self) -> float:
        """计算RSI信号评分 (-1到1)"""
        if self.state_map.rsi >= self.params_map.rsi_overbought:
            # 超买，看空
            return -1.0
        elif self.state_map.rsi <= self.params_map.rsi_oversold:
            # 超卖，看多
            return 1.0
        elif self.state_map.rsi > 50:
            # 偏多头
            return (self.state_map.rsi - 50) / (self.params_map.rsi_overbought - 50) * 0.5
        else:
            # 偏空头
            return (self.state_map.rsi - 50) / (50 - self.params_map.rsi_oversold) * 0.5

    def _calc_bb_signal_score(self) -> float:
        """计算布林带信号评分 (-1到1)"""
        # 基于布林带百分比位置
        if self.state_map.bb_percent >= 0.8:
            # 接近上轨，看空
            return -0.8
        elif self.state_map.bb_percent <= 0.2:
            # 接近下轨，看多
            return 0.8
        else:
            # 中性区域，根据位置给分
            return (self.state_map.bb_percent - 0.5) * 2 * 0.3

    def _calc_volume_signal_score(self) -> float:
        """计算成交量信号评分 (-1到1)"""
        if self.state_map.volume_ratio >= self.params_map.volume_threshold:
            # 成交量放大，增强信号
            return min(1.0, (self.state_map.volume_ratio - 1) / self.params_map.volume_threshold)
        else:
            # 成交量萎缩，减弱信号
            return -0.3

    def _generate_final_signals(self, current_position: int, trend_filter: bool):
        """生成最终交易信号"""
        
        # 只有在趋势强度足够时才考虑信号
        if not trend_filter:
            return
        
        # 多头信号
        if self.state_map.bull_signal_strength >= self.params_map.signal_threshold:
            if current_position < 0:
                # 有空仓，平空
                self.cover_signal = True
                self.logger.debug("综合多头信号-平空")
            elif current_position == 0 and self.params_map.trade_direction == "buy":
                # 无持仓且允许做多，开多
                self.buy_signal = True
                self.logger.debug("综合多头信号-开多")
        
        # 空头信号
        elif self.state_map.bear_signal_strength <= -self.params_map.signal_threshold:
            if current_position > 0:
                # 有多仓，平多
                self.sell_signal = True
                self.logger.debug("综合空头信号-平多")
            elif current_position == 0 and self.params_map.trade_direction == "sell":
                # 无持仓且允许做空，开空
                self.short_signal = True
                self.logger.debug("综合空头信号-开空")

    def _reset_signals(self):
        """重置所有信号"""
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False

    def get_trade_price(self) -> tuple[float, float]:
        """统一处理价格获取"""
        if self.tick:
            if self.params_map.price_type == "D1":
                return self.tick.ask_price1, self.tick.bid_price1
            else:
                return self.tick.ask_price2, self.tick.bid_price2
        else:
            close_price = getattr(self, '_last_close', 0)
            return close_price, close_price

    def _update_trade_prices(self, kline: KLineData):
        """更新交易价格"""
        self._last_close = kline.close
        self.long_price, self.short_price = self.get_trade_price()

    def exec_signal_enhanced(self):
        """增强信号执行系统"""
        self.signal_price = 0

        try:
            position = self.get_position(self.params_map.instrument_id)
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            return

        # 优先处理平仓信号
        if self._execute_close_signals(position):
            return

        # 处理开仓信号
        if position.net_position == 0:
            self._execute_open_signals()

    def _execute_close_signals(self, position) -> bool:
        """执行平仓信号"""
        # 平多仓
        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.short_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                self.logger.info(f"平多仓: 价格={self.short_price}, 数量={position.net_position}, "
                               f"信号强度={abs(self.state_map.bear_signal_strength):.3f}")
            return True
        
        # 平空仓
        if position.net_position < 0 and self.cover_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=abs(position.net_position),
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                self.logger.info(f"平空仓: 价格={self.long_price}, 数量={abs(position.net_position)}, "
                               f"信号强度={self.state_map.bull_signal_strength:.3f}")
            return True
        
        return False

    def _execute_open_signals(self):
        """执行开仓信号"""
        # 开空仓
        if self.short_signal:
            self.signal_price = -self.short_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.short_price,
                    order_direction="sell"
                )
                self.state_map.pending_order = True
                self.logger.info(f"开空仓: 价格={self.short_price}, 数量={self.params_map.order_volume}, "
                               f"信号强度={abs(self.state_map.bear_signal_strength):.3f}")
        
        # 开多仓
        elif self.buy_signal:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                self.state_map.pending_order = True
                self.logger.info(f"开多仓: 价格={self.long_price}, 数量={self.params_map.order_volume}, "
                               f"信号强度={self.state_map.bull_signal_strength:.3f}")

    def _check_open_signal_after_close(self):
        """平仓成交后检查开仓信号"""
        try:
            position = self.get_position(self.params_map.instrument_id)
            
            if position.net_position == 0:
                if self.buy_signal and self.params_map.trade_direction == "buy":
                    self._execute_open_signals()
                elif self.short_signal and self.params_map.trade_direction == "sell":
                    self._execute_open_signals()
                    
        except Exception as e:
            self.logger.error(f"平仓后检查开仓信号失败: {e}")

    def _update_display(self, kline: KLineData):
        """更新显示"""
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()